# AzureCNArchaeologist 系统架构文档 (v3)

本文档详细描述了 `AzureCNArchaeologist` 项目在 **v3（三策略模型）** 重构后的系统架构、核心组件职责和完整的调用链。

## 1. 核心设计理念

项目采用清晰的、可扩展的**分层管道（Layered Pipeline）架构**。每一层都有明确的单一职责，通过定义好的接口与相邻层通信，实现了高度的模块化和低耦合。

其核心处理流程遵循**策略模式（Strategy Pattern）**，能够根据不同的页面复杂度，动态选择最合适的解析策略，从而优雅地处理各类页面。

## 2. 架构分层详解

系统从上至下共分为6个主要层次：

### 层次 1: 命令行接口 (CLI)
- **组件**: `cli.py`
- **职责**: 作为项目的统一**用户入口**，负责解析命令行参数，并启动相应的任务。
- **关键交互**:
    - 接收用户输入的 `extract` 命令及参数。
    - **调用并启动**客户端层 (`EnhancedCMSExtractor`)。

### 层次 2: 客户端 (Client Layer)
- **组件**: `src/extractors/enhanced_cms_extractor.py`
- **职责**: 作为系统的**门面（Facade）**，为上层提供一个简洁、稳定的API，并屏蔽内部的复杂实现。
- **关键交互**:
    - 被CLI层调用，执行 `extract_cms_content()`。
    - **委托**所有核心提取任务给协调层 (`ExtractionCoordinator`)。

### 层次 3: 协调层 (Coordination Layer)
- **组件**: `src/core/extraction_coordinator.py`
- **职责**: 系统的**大脑和调度中心**。它不执行具体的提取逻辑，而是按顺序编排和调度下层的所有组件来完成复杂的提取工作流。
- **关键交互**:
    - **全局预处理**: 调用 `preprocess_image_paths` 对整个HTML文档进行图片路径的统一标准化。
    - **决策**: 调用 `StrategyManager` 来决定使用哪种提取策略。
    - **创建**: 根据决策结果，调用 `StrategyFactory` 来创建具体的策略实例。
    - **执行**: 调用策略实例的 `extract_flexible_content()` 方法。
    - **返回**: 将最终处理好的数据返回给客户端层。

### 层次 4: 决策与创建 (Decision & Creation Layers)
- **A. 决策组件**: `src/core/strategy_manager.py`
    - **职责**: **决策者**。根据页面分析结果，回答“**用哪个策略？**”的问题。
    - **关键交互**:
        - 使用 `PageAnalyzer` 分析HTML页面结构（筛选器、Tabs等）。
        - 返回一个包含策略选择（如 `StrategyType.COMPLEX`）的 `ExtractionStrategy` 数据对象。

- **B. 创建组件**: `src/strategies/strategy_factory.py`
    - **职责**: **建造者**。根据决策结果，回答“**如何创建这个策略实例？**”的问题。
    - **关键交互**:
        - 接收 `StrategyManager` 的决策。
        - 从内部的策略注册表中查找对应的策略类，并创建其对象实例。

### 层次 5: 策略执行层 (Strategy Execution Layer)
- **组件**:
    - `src/strategies/simple_static_strategy.py`
    - `src/strategies/region_filter_strategy.py`
    - `src/strategies/complex_content_strategy.py`
- **职责**: **执行者**。包含针对不同页面类型的、高度定制化的提取逻辑。这是真正“干活”的地方。
- **关键交互**:
    - 执行 `extract_flexible_content()` 方法。
    - 大量使用 `src/utils/` 下的**工具类**（如 `FlexibleBuilder`, `SectionExtractor`, `RegionProcessor`）来辅助解析HTML和构建最终数据。

### 层次 6: 导出层 (Export Layer)
- **组件**: `src/exporters/flexible_content_exporter.py`
- **职责**: **格式化与输出**。负责将内存中结构化的数据对象，序列化并写入到最终的文件中。
- **关键交互**:
    - 接收来自协调层的最终Python字典数据。
    - **执行最后的占位符替换**（将 `{img_hostname}` 替换为统一的 `{base_url}`）。
    - 将字典序列化为格式化的JSON字符串，并写入文件。

## 3. 核心工作流：`extract` 命令调用链

当用户执行 `uv run cli.py extract ...` 时，系统内部的调用流程如下：

1.  **`cli.py`** 解析命令，实例化 `EnhancedCMSExtractor` 并调用其 `extract_cms_content()` 方法。
2.  **`EnhancedCMSExtractor`** 将请求直接委托给 `ExtractionCoordinator`。
3.  **`ExtractionCoordinator`** 开始执行协调任务：
    a.  加载并使用 `BeautifulSoup` 解析HTML文件。
    b.  对整个`soup`对象进行全局图片路径标准化。
    c.  调用 `StrategyManager` 获取页面类型和应使用的策略（例如，返回 `StrategyType.COMPLEX`）。
    d.  将策略类型传递给 `StrategyFactory`，后者创建并返回一个 `ComplexContentStrategy` 的实例。
    e.  调用 `ComplexContentStrategy` 实例的 `extract_flexible_content()` 方法。
4.  **`ComplexContentStrategy`** 开始执行：
    a.  调用 `FilterDetector` 和 `TabDetector` 分析筛选器和Tab。
    b.  调用 `RegionProcessor` 处理与区域相关的内容。
    c.  调用 `SectionExtractor` 提取Banner, QA等通用区块。
    d.  调用 `FlexibleBuilder` 将所有提取出的内容组装成符合 `FlexibleContentPage` Schema的Python字典。
    e.  将组装好的字典返回给 `ExtractionCoordinator`。
5.  **`ExtractionCoordinator`** 收到数据后，添加最后的元数据，然后将完整的字典返回给 `cli.py`。
6.  **`cli.py`** 接收到最终数据，实例化 `FlexibleContentExporter`。
7.  **`FlexibleContentExporter`** 对数据进行最后的处理（如占位符替换），将其序列化为JSON字符串，并保存到 `.json` 文件中。

## 4. 核心数据模型

新架构主要围绕以下几个核心数据模型运作：

- **`StrategyType` (Enum)**: 定义了三种核心策略 `SIMPLE_STATIC`, `REGION_FILTER`, `COMPLEX`，以及一种特殊策略 `LARGE_FILE`。
- **`ExtractionStrategy` (Dataclass)**: 一个数据对象，用于在决策层和创建层之间传递策略选择的结果。
- **`Flexible...` (Dataclasses)**:
    - `FlexibleContentData`: 描述最终Flexible JSON输出的完整结构。
    - `FlexibleContentGroup`: 描述内容组的数据结构。
    - `FlexiblePageConfig`: 描述页面筛选器配置的数据结构。
    - `FlexibleCommonSection`: 描述通用区块（Banner, QA等）的数据结构。
