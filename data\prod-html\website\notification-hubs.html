<!DOCTYPE html>
<html lang="zh-CN">
 <head>
  <meta charset="utf-8"/>
  <meta content="IE=edge" http-equiv="X-UA-Compatible"/>
  <meta content="width=device-width, initial-scale=1.0, maximum-scale=2.0" name="viewport"/>
  <meta content="Azure, 微软云, Azure 通知中心, Azure Notification Hubs, 价格详情, 定价, 计费" name="keywords"/>
  <meta content="了解 Azure 通知中心（Notification Hubs）的价格详情。Azure 通知中心提供了一种可高度扩展的跨平台推送通知基础结构，可以帮您将推送通知同时广播至数百万用户，或针对不同用户对通知内容进行定制。1元试用即获1500元人民币的服务使用额度，也可直接购买成为 Azure 预付费客户。" name="description"/>
  <title>
   通知中心 - Azure云计算
  </title>
  <link href="../../../Static/Favicon/apple-touch-icon.png" rel="apple-touch-icon" sizes="180x180"/>
  <link href="../../../Static/Favicon/favicon.ico" rel="shortcut icon" type="image/x-icon"/>
  <link href="../../../Static/Favicon/favicon.ico" rel="icon" type="image/x-icon"/>
  <link href="../../../Static/Favicon/manifest.json" rel="manifest"/>
  <link color="#0078D4" href="../../../Static/Favicon/safari-pinned-tab.svg" rel="mask-icon"/>
  <meta content="/Static/Favicon/browserconfig.xml" name="msapplication-config"/>
  <meta content="#ffffff" name="theme-color"/>
  <link href="https://azure.microsoft.com/pricing/details/notification-hubs/" rel="canonical"/>
  <!-- BEGIN: Azure UI Style -->
  <link href="../../../Static/CSS/azureui.min.css" rel="stylesheet"/>
  <link href="../../../Static/CSS/common.min.css" rel="stylesheet"/>
  <!-- END: Azure UI Style -->
  <!-- BEGIN: Page Style -->
  <!-- END: Page Style -->
  <!-- BEGIN: Minified Page Style -->
  <link href="../../../Static/CSS/a05a0e4e99489d7b3cb548e24414e01adf779d77.css" rel="stylesheet"/>
  <!-- END: Minified Page Style -->
  <link href="../../../StaticService/css/service.min.css" rel="stylesheet"/>
 </head>
 <body class="zh-cn">
  <script>
   window.requireUrlArgs = "2019/6/24 6:51:27";
        window.currentLocale = "zh-CN";        
        window.headerTimestamp = "2019/1/23 8:25:24";
        window.footerTimestamp = "2019/1/8 8:07:06";
        window.locFileTimestamp = "2018/11/29 7:49:17";

        window.AcnHeaderFooter = {
            IsEnableQrCode: true,
            CurrentLang: window.currentLocale.toLowerCase(),
        };
  </script>
  <style>
   @media (min-width: 0) {
            .acn-header-placeholder {
                height: 48px; 
                width: 100%;
            }
        }

        @media (min-width: 980px) {
            .acn-header-placeholder {
                height: 89px; 
                width: 100%;
            }
        }

        .acn-header-placeholder {
            background-color: #1A1A1A;
        }

        .acn-header-service{
            position: absolute; 
            top: 0;
            width: 100%;
        }
  </style>
  <div class="acn-header-container">
   <div class="acn-header-placeholder">
   </div>
   <div class="public_headerpage">
   </div>
  </div>
  <!-- BEGIN: Documentation Content -->
  <div class="content">
   <div class="container">
    <div class="row">
     <div class="col-md-12">
      <div class="bread-crumb hidden-sm hidden-xs">
       <ul>
        <li>
         <span>
         </span>
        </li>
       </ul>
      </div>
     </div>
    </div>
    <div class="single-page">
     <div class="row">
      <div class="col-md-2">
       <div class="documentation-navigation left-navigation hidden-xs hidden-sm">
        <div class="loader">
         <i class="circle">
         </i>
         <i class="circle">
         </i>
         <i class="circle">
         </i>
         <i class="circle">
         </i>
         <i class="circle">
         </i>
        </div>
       </div>
      </div>
      <div class="col-md-10 pure-content">
       <div class="select left-navigation-select hidden-md hidden-lg">
        <select>
         <option selected="selected">
          加载中...
         </option>
        </select>
        <span class="icon icon-arrow-top">
        </span>
       </div>
       <tags ms.date="09/30/2015" ms.service="notification-hubs" wacn.date="11/27/2015">
       </tags>
       <!-- BEGIN: Product-Detail-TopBanner -->
       <div class="common-banner col-top-banner" data-config="{'backgroundColor':'#e3f4ff', 'backgroundImage':'/Images/marketing-resource/css/notifications_hubs.png','imageHeight':'auto'}">
        <div class="common-banner-image">
         <div class="common-banner-title">
          <img src="/Images/marketing-resource/css/<EMAIL>"/>
          <h2>
           通知中心
           <span>
            Notification Hubs
           </span>
          </h2>
          <h4>
           从任何后端向任何平台发送推送通知
          </h4>
         </div>
        </div>
       </div>
       <!-- END: Product-Detail-TopBanner -->
       <div class="pricing-page-section">
        <p>
         Azure 通知中心提供了一种可高度扩展的跨平台推送通知基础结构，可以帮您将推送通知同时广播至数百万用户，或针对不同用户对通知内容进行定制。 您可将通知中心与任何可连接互联网的移动应用程序配合使用，无论该应用运行于 Azure 虚拟机、云服务、网站，或移动服务中，都可支持该功能。
        </p>
       </div>
       <!-- BEGIN: TAB-CONTROL -->
       <div class="technical-azure-selector tab-control-selector">
        <!-- BEGIN: TAB-CONTAINER-1 -->
        <div class="tab-control-container tab-active" id="tabContent1">
         <!-- BEGIN: Table1-Content-->
         <h2>
          定价详细信息
         </h2>
         <!--<h3>Web 应用</h3>-->
         <!-- <p>从 2016 年 4 月 1 日起，通知中心的价格下调 25.5%，以下是下调后的新价格：</p> -->
         <div class="tags-date">
          <div class="ms-date">
           *以下价格均为含税价格。
          </div>
          <br/>
          <div class="ms-date">
           *每月价格估算基于每个月 744 小时的使用量。
          </div>
         </div>
         <table cellpadding="0" cellspacing="0" width="100%">
          <tr>
           <th align="left">
           </th>
           <th align="left">
            <strong>
             免费
            </strong>
           </th>
           <th align="left">
            <strong>
             基本
            </strong>
           </th>
           <th align="left">
            <strong>
             标准
            </strong>
           </th>
          </tr>
          <tr>
           <td>
            基本费用
            <sup style="font-weight: normal;">
             1
            </sup>
            <br/>
            包含的推送数
           </td>
           <td>
            免费
            <br/>
            1 百万条
            <sup style="font-weight: normal;">
             2
            </sup>
           </td>
           <td>
            ¥ 46.16 / 月
            <br/>
            1 千万条
           </td>
           <td>
            ¥ 923.41 / 月
            <br/>
            1 千万条
           </td>
          </tr>
          <tr>
           <td>
            额外的推送
            <br/>
            1 千万 – 1 亿条 / 月
            <br/>
            超过 1 亿条 / 月
           </td>
           <td>
            <br/>
            N/A
            <br/>
            N/A
           </td>
           <td>
            <br/>
            ¥ 4.61 / 百万条
            <br/>
            ¥ 4.61 / 百万条
           </td>
           <td>
            <br/>
            ¥ 46.16 / 百万条
            <br/>
            ¥ 11.54 / 百万条
           </td>
          </tr>
          <tr>
           <td>
            活跃设备数
           </td>
           <td>
            无限制
           </td>
           <td>
            无限制
           </td>
           <td>
            无限制
           </td>
          </tr>
          <tr>
           <td>
            基本型 x-plat 推送至特定设备
           </td>
           <td>
            √
           </td>
           <td>
            √
           </td>
           <td>
            √
           </td>
          </tr>
          <tr>
           <td>
            广播（标签大小）
           </td>
           <td>
            限制为 10K
           </td>
           <td>
            限制为 10K
           </td>
           <td>
            无限制
           </td>
          </tr>
          <tr>
           <td>
            标签数（广播组）
           </td>
           <td>
            限制为 3K
           </td>
           <td>
            限制为 3K
           </td>
           <td>
            无限制
           </td>
          </tr>
          <tr>
           <td>
            自动缩放
           </td>
           <td>
           </td>
           <td>
            √
           </td>
           <td>
            √
           </td>
          </tr>
          <tr>
           <td>
            可查询的受众（需要注册）
           </td>
           <td>
           </td>
           <td>
           </td>
           <td>
            √
           </td>
          </tr>
          <tr>
           <td>
            计划推送
           </td>
           <td>
           </td>
           <td>
           </td>
           <td>
            √
           </td>
          </tr>
          <tr>
           <td>
            丰富的遥测
           </td>
           <td>
           </td>
           <td>
           </td>
           <td>
            √
           </td>
          </tr>
          <tr>
           <td>
            批量导入
           </td>
           <td>
           </td>
           <td>
           </td>
           <td>
            √
           </td>
          </tr>
          <tr>
           <td>
            多租户
           </td>
           <td>
           </td>
           <td>
           </td>
           <td>
            √
           </td>
          </tr>
         </table>
         <div class="tags-date">
          <div class="ms-date">
           <strong>
            <sup style="font-weight: normal;">
             1
            </sup>
           </strong>
           基本费用将按照每月 31 天进行均摊。
          </div>
          <div class="ms-date">
           <strong>
            <sup style="font-weight: normal;">
             2
            </sup>
           </strong>
           免费层的 1 百万推送数将以 UTC 时间自然月为基准进行统计。
          </div>
         </div>
         <!-- END: Table1-Content-->
        </div>
        <!-- END: TAB-CONTAINER-1 -->
       </div>
       <!-- END: TAB-CONTROL -->
       <div class="pricing-page-section">
        <div class="more-detail">
         <h2>
          常见问题
         </h2>
         <em id="notification_hubs_unfolded_all">
          全部展开
         </em>
         <ul>
          <li>
           <i class="icon icon-plus">
           </i>
           <div>
            <a id="Notification_hubs1">
             什么是 Azure Notification Hubs 服务百度云推送？
            </a>
            <section>
             <p>
              对于应用程序开发者来说，针对中国市场编写可以接收推送通知的 Android 应用程序很有挑战性。Android 手机通常不安装 Google Play 商店，而这些设备必须通过该商店才能从 GCM (Google Cloud Messaging) 接收通知。有很多不同的应用商店和推送服务，让这变得更加困难。
             </p>
             <p>
              今天我们宣布支持从 Azure Notification Hub 服务通过百度云推送向此类 Android 设备发送推送通知。这是除了 Azure Notification Hub 中针对 iOS、Windows Phone、Windows、Android 和 Kindle 的现有支持以外的附加支持。
             </p>
             <p>
              应用程序开发者必须登录到百度门户，注册成为百度开发者，创建一个云推送项目并获得应用程序相应的标识符（UserId 和 ChannelId），然后从 Azure 管理门户将标识符插入 Azure Notification Hub。此后，他们可以使用其客户端应用程序中更新的 Notification Hub Android SDK 向此 Notification Hub 注册该设备，然后使用更新的 Service Bus/Notification Hub.NET SDK 来发送推送通知，该通知将通过百度云推送服务传递到注册的 Android 设备。
             </p>
             <p>
              帮助开发的详细入门教程位于
              <a href="https://docs.azure.cn/notification-hubs/notification-hubs-baidu-china-android-notifications-get-started" id="Notification_price_cichu" style="color: #00a8d9;">
               此处
              </a>
              。
             </p>
            </section>
           </div>
          </li>
          <li>
           <i class="icon icon-plus">
           </i>
           <div>
            <a id="Notification_hubs2">
             现有通知中心用户在 2014 年 12 月 1 日后会发生什么事？
            </a>
            <section>
             <p>
              在 2014 年 12 月 1 日前注册通知中心的客户将自动迁移至对应服务层新的定价。因此如果客户原本在使用基本层服务，将自动迁移至基本层。若要获得更多帮助，请联系
              <a href="https://support.azure.cn/zh-cn/support/contact" id="Notification_price_newsuppert" style="color: #00a8d9;">
               支持
              </a>
              。
             </p>
            </section>
           </div>
          </li>
          <li>
           <i class="icon icon-plus">
           </i>
           <div>
            <a id="Notification_hubs3">
             基本层与标准层如何使用自动缩放？
            </a>
            <section>
             <p>
              在 2014 年 11 月 30 日前，您可以在管理门户中为您的名称空间指定单位数量的最小值（以确保为超出您目前所用的活跃设备或推送通知数量留出足够的容量）。您也可以设置希望服务总线根据名称空间的实际用量进行自动缩放时可使用的最大单位数。在每一天开始时（UTC 时间零点），我们将提供不少于您设置的最小单位数的容量，或为支持您当前活跃设备数所需最小单位数的容量。在这一天中，如果您的活跃设备数或推送数量超出当前提供的单位数，我们会提高该容量，最高可达您选择的单位数最大值（额外的容量成功供应之前，您可能会遇到一定的节流或较高延迟）。在这一天结束时（UTC 时间子夜），您将会根据这一天所用单位数量的最大值收费。如果您希望关闭自动缩放功能，则可将单位数量的最大数与最小数设置为相同值。
             </p>
             <p>
              从 2014 年 12月 1 日开始，基本和标准层服务不再需要自动缩放。客户可以按照目前公布的费率使用不限数量的推送。
             </p>
            </section>
           </div>
          </li>
          <li>
           <i class="icon icon-plus">
           </i>
           <div>
            <a id="Notification_hubs4">
             通知中心是否有任何形式的配额？
            </a>
            <section>
             <p>
              在 2014 年 11月 30日前，免费层服务每月允许发送的推送通知数按日均摊后为每天 3,333 条，基本层服务为每天 16,667 条，一个标准单位每天为 166,667 条。
             </p>
             <p>
              当一个名称空间达到每日推送数上限后，在这一天结束（UTC 时间子夜），或在标准层中选择更多单位，或升级至更高级的服务层以提高上限之前，该名称空间将无法继续发送通知。
             </p>
             <p>
              对于标准层服务，单位的数量可自动进行扩展或收缩。
             </p>
             <p>
              从 2014 年 12 月 1 日开始，由于基本费用和推送费用定价的变化，每日推送配额将不再生效。
             </p>
            </section>
           </div>
          </li>
          <li>
           <i class="icon icon-plus">
           </i>
           <div>
            <a id="Notification_hubs5">
             免费层对活跃设备的数量是否有限制？
            </a>
            <section>
             <p>
              在 2014 年 11月 30日之前，将继续对活跃设备数进行持续追踪。当活跃设备数达到上限后，新设备的注册操作将失败，直到活跃设备数低于上限（通过过期操作撤销注册的设备），或直到对标准层服务进行升级以扩大上限，或扩展基本层或标准层的单位数。
             </p>
             <p>
              从 2014 年 12 月 1 日开始，所有服务层对活跃设备数都将不再设置上限。
             </p>
            </section>
           </div>
          </li>
          <li>
           <i class="icon icon-plus">
           </i>
           <div>
            <a id="Notification_hubs6">
             每个推送中包含什么？
            </a>
            <section>
             <p>
              推送中包含提供给平台推送服务（例如 Windows 通知服务、Apple 推送通知服务、Google Cloud Messaging，以及微软推送通知服务）的所有通知。
             </p>
            </section>
           </div>
          </li>
          <li>
           <i class="icon icon-plus">
           </i>
           <div>
            <a id="Notification_hubs7">
             活跃设备是什么？
            </a>
            <section>
             <p>
              活跃设备是指可接收通知的设备，需使用 Google Cloud Messaging 或 Amazon Device Messaging 为设备定义具备唯一性的注册 ID，或使用 Windows 通知服务或微软推送通知服务为设备注册渠道 URI（统一资源标识符），或使用 Apple 推送通知服务为设备创建设备令牌。请注意，一个物理设备可在通知中心中算作多个活跃设备。
             </p>
            </section>
           </div>
          </li>
          <li>
           <i class="icon icon-plus">
           </i>
           <div>
            <a id="Notification_hubs8">
             广播和标签是什么？
            </a>
            <section>
             <p>
              广播是指您可以通过一个通知请求将推送通知发送到的设备数量。标签是指设备所订阅的关键字。广播的推送通知可发送给订阅了特定标签的所有设备。
             </p>
             <p>
              在 2014 年 11月 30日之前，广播功能的数量上限与您的通知中心服务层活动设备数上限保持相同。
             </p>
             <p>
              从 2014 年 12 月 1 日开始，通知中心的免费和基本层服务中，当您对某一受众发送广播时，最多可推送至 10,000 个设备。如果目标受众包含更多设备，则将随机选择 10,000 个设备进行发送，其余设备将无法接收到任何通知。
             </p>
            </section>
           </div>
          </li>
          <li>
           <i class="icon icon-plus">
           </i>
           <div>
            <a id="Notification_hubs9">
             名称空间是什么？基本和标准层每个名称空间可以使用多少个单位？
            </a>
            <section>
             <p>
              名称空间是一种分组机制，其中可能包含多个通知中心。在 2014 年 11月 30日之前，Azure 管理门户可以允许客户在标准层中扩展至每个名称空间最多 50 个单位。如果您的名称空间需要更大容量，请联系
              <a href="https://support.azure.cn/zh-cn/support/contact" id="Notification_price_suppert2">
               支持
              </a>
              。
             </p>
             <p>
              从 2014 年 12 月 1 日开始，将根据基本费用和推送的通知数进行计费，因此单位的限制将被取消。
             </p>
            </section>
           </div>
          </li>
         </ul>
         <p>
          有关通知中心的更多常见问题，请参阅
          <a href="http://msdn.microsoft.com/zh-cn/library/jj927170.aspx" id="Notification_price_MSDN" style="color: #00a8d9;">
           这篇 MSDN 文章
          </a>
          。
         </p>
        </div>
       </div>
       <!-- <div class="pricing-page-section">
                <h2>上市地区</h2>
                <p>通知中心在以下区域提供：</p>
                <table cellpadding="0" cellspacing="0" class="table-col6">
                    <tr>
                        <th align="left"><strong>地域</strong></th>
                        <th align="left"><strong>区域</strong></th>
                    </tr>
                    <tr>
                        <td>中国大陆</td>
                        <td>中国东部数据中心 , 中国北部数据中心</td>
                    </tr>
                </table>
                 -->
       <div class="pricing-page-section">
        <h2>
         支持和服务级别协议
        </h2>
        <p>
         如有任何疑问或需要帮助，请访问
         <a href="https://support.azure.cn/zh-cn/support/contact" id="notification-contact-page">
          Azure 支持
         </a>
         选择自助服务或者其他任何方式联系我们获得支持。
        </p>
        <p>
         对于通知中心的基本和标准层服务，我们确保至少在 99.9% 的时间里，在基本或标准层运行的通知中心服务将通过恰当配置的应用程序成功发送通知或执行注册管理操作。若要了解有关我们的服务器级别协议的详细信息，请访问
         <a href="../../../support/sla/notification-hubs" id="pricing_notification_sla">
          服务级别协议
         </a>
         页。
        </p>
       </div>
       <!--BEGIN: Support and service code chunk-->
       <!--

            <h2>支持和服务级别协议</h2>
            <p>Azure 支持功能：</p>
            <p>我们免费向用户提供以下支持服务：</p>
            <table cellpadding="0" cellspacing="0" class="table-col6">
                <tr>
                    <th align="left">&nbsp;</th>
                    <th align="left"><strong>是否支持</strong></th>
                </tr>
                <tr>
                    <td>计费和订阅管理</td>
                    <td><i class="icon icon-tick"></i></td>
                </tr>
                <tr>
                    <td>服务仪表板</td>
                    <td><i class="icon icon-tick"></i></td>
                </tr>
                <tr>
                    <td>Web事件提交</td>
                    <td><i class="icon icon-tick"></i></td>
                </tr>
                <tr>
                    <td>中断/修复不受限制</td>
                    <td><i class="icon icon-tick"></i></td>
                </tr>
                <tr>
                    <td>电话支持</td>
                    <td><i class="icon icon-tick"></i></td>
                </tr>
                <tr>
                    <td>ICP备案支持</td>
                    <td><i class="icon icon-tick"></i></td>
                </tr>
            </table>
            <p>您可以<a href="/support/support-ticket-form/?l=zh-cn" id="vm-sla-sup">在线申请支持</a>，也可以通过服务热线联系我们。</p>
            <h2>服务热线：</h2>
            <ul>
                <li>400-089-0365</li>
                <li>010-84563652</li>
            </ul>
            <p>社区帮助：<a href="https://social.msdn.microsoft.com/Forums/zh-cn/home?forum=windowsazurezhchs" id="pricing_vm_help">访问MSDN</a></p>
            <p>更多支持信息，请访问<a href="/support/plans/" id="stor-sla-info">Azure 支持计划</a></p>
              
      -->
       <!--END: Support and service code chunk-->
       <!--BEGIN: Support and service code chunk-->
       <!--END: Support and service code chunk-->
      </div>
     </div>
    </div>
   </div>
  </div>
  <!-- END: Documentation Content -->
  <!-- BEGIN: Footer -->
  <div class="public_footerpage">
  </div>
  <!--END: Common sidebar-->
  <link href="../../../Static/CSS/Localization/zh-cn.css" rel="stylesheet"/>
  <script type="text/javascript">
   function getAntiForgeryToken() {
        var token = '<input name="__RequestVerificationToken" type="hidden" value="s6Q8kMA9sL2oMXxtppybaKQO9XDD5Up3VMxmotdrcAZRSatziDIECMeGL-MWNSplx3uoQ60PYNJFgwlRYopcsNjIT6ofjBmhWUZbLuy39qw1" />';
        token = $(token).val();
        return token;
    }
    function setLocaleCookie(localeVal) {
        var Days = 365 * 10; // Ten year expiration
        var exp = new Date();

        var hostName = window.location.hostname;
        var secondLevelDomain = hostName.substring(hostName.lastIndexOf(".", hostName.lastIndexOf(".") - 1));

        exp.setTime(exp.getTime() + Days * 24 * 60 * 60 * 1000);
        document.cookie = "acn_selected_locale=" + localeVal + ";domain=" + secondLevelDomain + "; path=/;expires=" + exp.toGMTString();
    }

    setLocaleCookie(window.currentLocale);
  </script>
  <script type="text/javascript">
   var MARKETING_STORAGE = '/blob/marketing-resource/Content';
    var TECHNICAL_STORAGE = '/blob/tech-content/';
    var EXTERNAL_RESOURCE_STORAGE = '/blob/external-resource/';
    var EnablePricingSync = 'false';
  </script>
  <!-- BEGIN: Minified RequireJs -->
  <script src="../../../Static/Scripts/global.config.js" type="text/javascript">
  </script>
  <script src="../../../Static/Scripts/require.js" type="text/javascript">
  </script>
  <script src="../../../Static/Scripts/4552a1505e111da8c71780b349b683700a6158fa.js" type="text/javascript">
  </script>
  <!-- END: Minified RequireJs -->
  <!-- begin JSLL -->
  <script src="https://az725175.vo.msecnd.net/scripts/jsll-4.js">
  </script>
  <script type="text/javascript">
   (function () {
            var config = {
                cookiesToCollect: ["_mkto_trk"],
                syncMuid: true,
                ix: {
                    a: true,
                    g: true
                },
                coreData: {
                    appId: 'AzureCN',
                    market: 'zh-cn',
                }
            };
            awa.init(config);
        })();
  </script>
  <!-- end JSLL -->
  <script src="../../../Static/Scripts/plugins/cookie/jquery.cookie.js" type="text/javascript">
  </script>
  <script src="../../../Static/Scripts/wacndatatracker.js" type="text/javascript">
  </script>
  <script src="/common/useCommon.js" type="text/javascript">
  </script>
 </body>
</html>
