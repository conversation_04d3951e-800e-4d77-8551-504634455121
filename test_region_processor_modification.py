#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试RegionProcessor的修改
验证从filter_analysis参数中获取区域信息的功能
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from core.region_processor import RegionProcessor
from bs4 import BeautifulSoup

def test_get_regions_from_filter_analysis():
    """测试从filter_analysis获取区域信息"""
    print("🧪 测试从filter_analysis获取区域信息...")
    
    # 创建RegionProcessor实例
    processor = RegionProcessor()
    
    # 测试数据 - 模拟您提供的filter_analysis格式
    filter_analysis = {
        'has_region': True, 
        'has_software': True, 
        'region_options': [
            {'href': '#north-china3', 'label': '中国北部3', 'value': 'north-china3'}, 
            {'href': '#east-china2', 'label': '中国东部 2', 'value': 'east-china2'}, 
            {'href': '#north-china2', 'label': '中国北部 2', 'value': 'north-china2'}, 
            {'href': '#east-china', 'label': '中国东部', 'value': 'east-china'}, 
            {'href': '#north-china', 'label': '中国北部', 'value': 'north-china'}
        ], 
        'region_visible': True, 
        'software_options': [
            {'href': '#tabContent1', 'label': 'API Management', 'value': 'API Management'}
        ], 
        'software_visible': False
    }
    
    # 测试新方法
    regions = processor._get_regions_from_filter_analysis(filter_analysis)
    
    print(f"✅ 获取到的区域: {regions}")
    
    # 验证结果
    expected_regions = ['north-china3', 'east-china2', 'north-china2', 'east-china', 'north-china']
    
    if regions == expected_regions:
        print("✅ 测试通过：区域提取正确")
        return True
    else:
        print(f"❌ 测试失败：期望 {expected_regions}，实际 {regions}")
        return False

def test_extract_region_contents_with_filter_analysis():
    """测试extract_region_contents方法使用filter_analysis"""
    print("\n🧪 测试extract_region_contents方法...")
    
    # 创建RegionProcessor实例
    processor = RegionProcessor()
    
    # 创建简单的HTML
    html_content = """
    <html>
        <body>
            <div class="tab-content">
                <p>测试内容</p>
            </div>
        </body>
    </html>
    """
    soup = BeautifulSoup(html_content, 'html.parser')
    
    # 测试数据
    filter_analysis = {
        'has_region': True, 
        'region_options': [
            {'href': '#north-china3', 'label': '中国北部3', 'value': 'north-china3'}, 
            {'href': '#east-china2', 'label': '中国东部 2', 'value': 'east-china2'}
        ], 
        'software_options': [
            {'href': '#tabContent1', 'label': 'API Management', 'value': 'API Management'}
        ]
    }
    
    # 调用extract_region_contents方法
    try:
        result = processor.extract_region_contents(
            soup=soup,
            html_file_path="test.html",
            filter_analysis=filter_analysis
        )
        
        print(f"✅ extract_region_contents执行成功，返回 {len(result)} 个区域")
        print(f"区域列表: {list(result.keys())}")
        return True
        
    except Exception as e:
        print(f"❌ extract_region_contents执行失败: {e}")
        return False

def test_fallback_to_html_detection():
    """测试回退到HTML检测的功能"""
    print("\n🧪 测试回退到HTML检测...")
    
    processor = RegionProcessor()
    
    # 创建包含区域信息的HTML
    html_content = """
    <html>
        <body>
            <select id="region-select">
                <option value="north-china">中国北部</option>
                <option value="east-china">中国东部</option>
            </select>
            <div class="tab-content">
                <p>测试内容</p>
            </div>
        </body>
    </html>
    """
    soup = BeautifulSoup(html_content, 'html.parser')
    
    # 测试空的filter_analysis（应该回退到HTML检测）
    filter_analysis = {}
    
    try:
        result = processor.extract_region_contents(
            soup=soup,
            html_file_path="test.html",
            filter_analysis=filter_analysis
        )
        
        print(f"✅ 回退机制工作正常，返回 {len(result)} 个区域")
        print(f"区域列表: {list(result.keys())}")
        return True
        
    except Exception as e:
        print(f"❌ 回退机制失败: {e}")
        return False

if __name__ == "__main__":
    print("🚀 开始测试RegionProcessor修改...")
    
    success_count = 0
    total_tests = 3
    
    # 运行测试
    if test_get_regions_from_filter_analysis():
        success_count += 1
        
    if test_extract_region_contents_with_filter_analysis():
        success_count += 1
        
    if test_fallback_to_html_detection():
        success_count += 1
    
    print(f"\n📊 测试结果: {success_count}/{total_tests} 个测试通过")
    
    if success_count == total_tests:
        print("🎉 所有测试通过！修改成功！")
    else:
        print("⚠ 部分测试失败，请检查代码")
