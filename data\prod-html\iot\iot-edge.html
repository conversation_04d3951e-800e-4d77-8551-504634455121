<!DOCTYPE html>
<html lang="zh-CN">
 <head>
  <meta charset="utf-8"/>
  <meta content="IE=edge" http-equiv="X-UA-Compatible"/>
  <meta content="width=device-width, initial-scale=1.0, maximum-scale=2.0" name="viewport"/>
  <meta content="Azure, 物联网, Azure IoT 边缘, 价格" name="keywords"/>
  <meta content="了解 IoT 边缘（Azure IoT Edge）价格详情。将云服务部署到 IoT 设备。无前期成本。即用即付。免费试用。1元试用即获1500元人民币的服务使用额度，也可直接购买成为 Azure 预付费客户，尽享最高 99.99% 的服务级别协议。" name="description"/>
  <title>
   Azure IoT 边缘服务定价 - Azure云计算
  </title>
  <link href="../../../Static/Favicon/apple-touch-icon.png" rel="apple-touch-icon" sizes="180x180"/>
  <link href="../../../Static/Favicon/favicon.ico" rel="shortcut icon" type="image/x-icon"/>
  <link href="../../../Static/Favicon/favicon.ico" rel="icon" type="image/x-icon"/>
  <link href="../../../Static/Favicon/manifest.json" rel="manifest"/>
  <link color="#0078D4" href="../../../Static/Favicon/safari-pinned-tab.svg" rel="mask-icon"/>
  <meta content="/Static/Favicon/browserconfig.xml" name="msapplication-config"/>
  <meta content="#ffffff" name="theme-color"/>
  <link href="https://azure.microsoft.com/pricing/details/iot-edge" rel="canonical"/>
  <!-- BEGIN: Azure UI Style -->
  <link href="../../../Static/CSS/azureui.min.css" rel="stylesheet"/>
  <link href="../../../Static/CSS/common.min.css" rel="stylesheet"/>
  <!-- END: Azure UI Style -->
  <!-- BEGIN: Page Style -->
  <!-- END: Page Style -->
  <!-- BEGIN: Minified Page Style -->
  <link href="../../../Static/CSS/a05a0e4e99489d7b3cb548e24414e01adf779d77.css" rel="stylesheet"/>
  <!-- END: Minified Page Style -->
  <link href="../../../StaticService/css/service.min.css" rel="stylesheet"/>
 </head>
 <body class="zh-cn">
  <script>
   window.requireUrlArgs = "2019/6/24 6:51:27";
        window.currentLocale = "zh-CN";        
        window.headerTimestamp = "2019/1/23 8:25:24";
        window.footerTimestamp = "2019/1/8 8:07:06";
        window.locFileTimestamp = "2018/11/29 7:49:17";

        window.AcnHeaderFooter = {
            IsEnableQrCode: true,
            CurrentLang: window.currentLocale.toLowerCase(),
        };
  </script>
  <style>
   @media (min-width: 0) {
            .acn-header-placeholder {
                height: 48px; 
                width: 100%;
            }
        }

        @media (min-width: 980px) {
            .acn-header-placeholder {
                height: 89px; 
                width: 100%;
            }
        }

        .acn-header-placeholder {
            background-color: #1A1A1A;
        }

        .acn-header-service{
            position: absolute; 
            top: 0;
            width: 100%;
        }
  </style>
  <div class="acn-header-container">
   <div class="acn-header-placeholder">
   </div>
   <div class="public_headerpage">
   </div>
  </div>
  <!-- BEGIN: Documentation Content -->
  <div class="content">
   <div class="container">
    <div class="row">
     <div class="col-md-12">
      <div class="bread-crumb hidden-sm hidden-xs">
       <ul>
        <li>
         <span>
         </span>
        </li>
       </ul>
      </div>
     </div>
    </div>
    <div class="single-page">
     <div class="row">
      <div class="col-md-2">
       <div class="documentation-navigation left-navigation hidden-xs hidden-sm">
        <div class="loader">
         <i class="circle">
         </i>
         <i class="circle">
         </i>
         <i class="circle">
         </i>
         <i class="circle">
         </i>
         <i class="circle">
         </i>
        </div>
       </div>
      </div>
      <div class="col-md-10 pure-content">
       <div class="select left-navigation-select hidden-md hidden-lg">
        <select>
         <option selected="selected">
          加载中...
         </option>
        </select>
        <span class="icon icon-arrow-top">
        </span>
       </div>
       <tags ms.date="09/30/2015" ms.service="iot-edge" wacn.date="11/27/2015">
       </tags>
       <!-- BEGIN: Product-Detail-TopBanner -->
       <div class="common-banner col-top-banner" data-config="{'backgroundColor':'#e3f4ff', 'backgroundImage':'/Images/marketing-resource/css/azure-iot-edge.png','imageHeight':'auto'}">
        <div class="common-banner-image">
         <div class="common-banner-title">
          <img src="/Images/marketing-resource/media/images/production/azure-iot-edge80.svg"/>
          <h2>
           Azure IoT 边缘
           <span>
            Azure IoT Edge
           </span>
          </h2>
          <h4>
           将云智能和分析扩展到边缘设备
          </h4>
         </div>
        </div>
       </div>
       <!-- END: Product-Detail-TopBanner -->
       <div class="pricing-page-section">
        <p>
         Azure IoT Edge 将云服务部署到 IoT 设备，让你可以在边缘设备本地处理除数、运行分析并获取见解。这是通过部署人工智能、Azure Functions 和 Azure 流分析等云服务，并从 Azure IoT 中心管理边缘设备实现的。还可部署自定义代码，从而在边缘设备上部署自定义的复杂业务逻辑。Azure IoT Edge 可在云和边缘实现代码对称，简化开发工作，让开发者能够专注于构建对最终用户而言有用的功能，而无需管理基础结构。
        </p>
       </div>
       <!-- BEGIN: TAB-CONTROL -->
       <div class="technical-azure-selector tab-control-selector">
        <!-- BEGIN: TAB-CONTAINER-1 -->
        <div class="tab-control-container tab-active" id="tabContent1">
         <!-- BEGIN: Table1-Content-->
         <h2>
          定价详细信息
         </h2>
         <p>
          需要使用 Azure IoT 中心，才能安全管理通过 Azure IoT Edge 部署到边缘的设备和服务。Azure IoT Edge 可在装有 Windows 或 Linux 的任意硬件上运行。将 Azure IoT Edge 部署到内存和其他资源充足的 IoT 设备，通常是网关类设备。
         </p>
         <p>
          Azure IoT 中心
          <a href="../../home/<USER>/iot-hub/index.html">
           详细信息
          </a>
          <a href="../iot-hub/index.html">
           定价
          </a>
         </p>
         <p>
          Azure IoT Edge 可免费使用。通过Azure IoT Edge，可在边缘运行多项 Azure 服务。在 IoT Edge 运行的 Azure 服务将根据其各自的定价进行计费。
         </p>
         <p>
          有关可在 Azure IoT Edge 上运行的服务的更多信息和定价详情，请访问以下页面：
         </p>
         <p>
          Azure Functions
          <a href="../../home/<USER>/azure-functions/index.html">
           详细信息
          </a>
          <a href="../azure-functions/index.html">
           定价
          </a>
         </p>
         <p>
          流分析
          <a href="../../home/<USER>/stream-analytics/index.html">
           详细信息
          </a>
          <a href="../stream-analytics/index.html">
           定价
          </a>
         </p>
        </div>
        <!-- END: TAB-CONTAINER-1 -->
       </div>
       <!-- END: TAB-CONTROL -->
       <div class="pricing-page-section">
        <div class="more-detail">
         <h2>
          常见问题
         </h2>
         <em>
          全部展开
         </em>
         <ul>
          <li>
           <i class="icon icon-plus">
           </i>
           <div>
            <a id="ih-que-q1">
             IoT Edge 的使用如何计费？
            </a>
            <section>
             <p>
              Azure IoT Edge 本身是免费的，但需要使用 Azure IoT 中心来安全管理设备和边缘部署。通过选择通过 IoT Edge 使用 Azure 服务，则将根据在边缘使用特定服务的计费模型进行计费。例如，如果选择在 IoT Edge 上运行 Azure 流分析，则将根据 IoT 中心使用情况和 Azure 流分析边缘定价进行计费。
             </p>
            </section>
           </div>
          </li>
         </ul>
        </div>
       </div>
       <div class="pricing-page-section">
        <h2>
         支持和服务级别协议
        </h2>
        <p>
         如有任何疑问或需要帮助，请访问
         <a href="https://support.azure.cn/zh-cn/support/contact" id="iot-contact-page">
          Azure 支持
         </a>
         选择自助服务或者其他任何方式联系我们获得支持。
        </p>
        <p>
         IoT 边缘是免费版不提供服务级别协议。
        </p>
        <p>
         若要了解有关我们的服务级别协议的详细信息，请访问
         <a href="../../../support/legal/sla/index.html" id="pricing_iot-edge_sla">
          服务级别协议
         </a>
         页。
        </p>
       </div>
       <!--BEGIN: Support and service code chunk-->
       <!--

            <h2>支持和服务级别协议</h2>
            <p>Azure 支持功能：</p>
            <p>我们免费向用户提供以下支持服务：</p>
            <table cellpadding="0" cellspacing="0" class="table-col6">
                <tr>
                    <th align="left">&nbsp;</th>
                    <th align="left"><strong>是否支持</strong></th>
                </tr>
                <tr>
                    <td>计费和订阅管理</td>
                    <td><i class="icon icon-tick"></i></td>
                </tr>
                <tr>
                    <td>服务仪表板</td>
                    <td><i class="icon icon-tick"></i></td>
                </tr>
                <tr>
                    <td>Web事件提交</td>
                    <td><i class="icon icon-tick"></i></td>
                </tr>
                <tr>
                    <td>中断/修复不受限制</td>
                    <td><i class="icon icon-tick"></i></td>
                </tr>
                <tr>
                    <td>电话支持</td>
                    <td><i class="icon icon-tick"></i></td>
                </tr>
                <tr>
                    <td>ICP备案支持</td>
                    <td><i class="icon icon-tick"></i></td>
                </tr>
            </table>
            <p>您可以<a href="/support/support-ticket-form/?l=zh-cn" id="vm-sla-sup">在线申请支持</a>，也可以通过服务热线联系我们。</p>
            <h2>服务热线：</h2>
            <ul>
                <li>400-089-0365</li>
                <li>010-84563652</li>
            </ul>
            <p>社区帮助：<a href="https://social.msdn.microsoft.com/Forums/zh-cn/home?forum=windowsazurezhchs" id="pricing_vm_help">访问MSDN</a></p>
            <p>更多支持信息，请访问<a href="/support/plans/" id="stor-sla-info">Azure 支持计划</a></p>
              
        -->
       <!--END: Support and service code chunk-->
       <!--BEGIN: Support and service code chunk-->
       <!--END: Support and service code chunk-->
      </div>
     </div>
    </div>
   </div>
  </div>
  <!-- END: Documentation Content -->
  <!-- BEGIN: Footer -->
  <div class="public_footerpage">
  </div>
  <!--END: Common sidebar-->
  <link href="../../../Static/CSS/Localization/zh-cn.css" rel="stylesheet"/>
  <script type="text/javascript">
   function getAntiForgeryToken() {
        var token = '<input name="__RequestVerificationToken" type="hidden" value="yHclh7aImUZ2-9giTkarEvN9TUI00BkCz_U5rxJ9tmwbd18cf_2yuTCJLubZKIK-zWN8Pc9rGQYlWa3bIUWY8iFFJKeN7ktzNpxOdyWxvyM1" />';
        token = $(token).val();
        return token;
    }
    function setLocaleCookie(localeVal) {
        var Days = 365 * 10; // Ten year expiration
        var exp = new Date();

        var hostName = window.location.hostname;
        var secondLevelDomain = hostName.substring(hostName.lastIndexOf(".", hostName.lastIndexOf(".") - 1));

        exp.setTime(exp.getTime() + Days * 24 * 60 * 60 * 1000);
        document.cookie = "acn_selected_locale=" + localeVal + ";domain=" + secondLevelDomain + "; path=/;expires=" + exp.toGMTString();
    }

    setLocaleCookie(window.currentLocale);
  </script>
  <script type="text/javascript">
   var MARKETING_STORAGE = '/blob/marketing-resource/Content';
    var TECHNICAL_STORAGE = '/blob/tech-content/';
    var EXTERNAL_RESOURCE_STORAGE = '/blob/external-resource/';
    var EnablePricingSync = 'false';
  </script>
  <!-- BEGIN: Minified RequireJs -->
  <script src="/Static/Scripts/global.config.js" type="text/javascript">
  </script>
  <script src="/Static/Scripts/require.js" type="text/javascript">
  </script>
  <script src="/Static/Scripts/4552a1505e111da8c71780b349b683700a6158fa.js" type="text/javascript">
  </script>
  <!-- END: Minified RequireJs -->
  <!-- begin JSLL -->
  <script src="https://az725175.vo.msecnd.net/scripts/jsll-4.js">
  </script>
  <script type="text/javascript">
   (function () {
            var config = {
                cookiesToCollect: ["_mkto_trk"],
                syncMuid: true,
                ix: {
                    a: true,
                    g: true
                },
                coreData: {
                    appId: 'AzureCN',
                    market: 'zh-cn',
                }
            };
            awa.init(config);
        })();
  </script>
  <!-- end JSLL -->
  <script src="../../Static/Scripts/plugins/cookie/jquery.cookie.js" type="text/javascript">
  </script>
  <script src="../../Static/Scripts/wacndatatracker.js" type="text/javascript">
  </script>
  <script src="/common/useCommon.js" type="text/javascript">
  </script>
 </body>
</html>
