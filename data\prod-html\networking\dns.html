<!DOCTYPE html>
<html lang="zh-CN">
 <head>
  <meta charset="utf-8"/>
  <meta content="IE=edge" http-equiv="X-UA-Compatible"/>
  <meta content="width=device-width, initial-scale=1.0, maximum-scale=2.0" name="viewport"/>
  <meta content="Azure, 微软云, Azure DNS, 价格详情, 定价, 计费" name="keywords"/>
  <meta content="了解 Azure DNS 的价格详情。1元试用即获1500元人民币的服务使用额度，也可直接购买成为 Azure 预付费客户，尽享最高 99.99% 的服务级别协议。" name="description"/>
  <title>
   Azure DNS 价格_Azure DNS 价格估算 - Azure云计算
  </title>
  <link href="../../../Static/Favicon/apple-touch-icon.png" rel="apple-touch-icon" sizes="180x180"/>
  <link href="../../../Static/Favicon/favicon.ico" rel="shortcut icon" type="image/x-icon"/>
  <link href="../../../Static/Favicon/favicon.ico" rel="icon" type="image/x-icon"/>
  <link href="../../../Static/Favicon/manifest.json" rel="manifest"/>
  <link color="#0078D4" href="../../../Static/Favicon/safari-pinned-tab.svg" rel="mask-icon"/>
  <meta content="/Static/Favicon/browserconfig.xml" name="msapplication-config"/>
  <meta content="#ffffff" name="theme-color"/>
  <link href="https://azure.microsoft.com/pricing/details/dns/" rel="canonical"/>
  <!-- BEGIN: Azure UI Style -->
  <link href="../../../Static/CSS/azureui.min.css" rel="stylesheet"/>
  <link href="../../../Static/CSS/common.min.css" rel="stylesheet"/>
  <!-- END: Azure UI Style -->
  <!-- BEGIN: Page Style -->
  <!-- END: Page Style -->
  <!-- BEGIN: Minified Page Style -->
  <link href="../../../Static/CSS/a05a0e4e99489d7b3cb548e24414e01adf779d77.css" rel="stylesheet"/>
  <!-- END: Minified Page Style -->
  <link href="../../../StaticService/css/service.min.css" rel="stylesheet"/>
 </head>
 <body class="zh-cn">
  <script>
   window.requireUrlArgs = "2019/6/24 6:51:27";
        window.currentLocale = "zh-CN";
        window.headerTimestamp = "2019/1/23 8:25:24";
        window.footerTimestamp = "2019/1/8 8:07:06";
        window.locFileTimestamp = "2018/11/29 7:49:17";

        window.AcnHeaderFooter = {
            IsEnableQrCode: true,
            CurrentLang: window.currentLocale.toLowerCase(),
        };
  </script>
  <style>
   @media (min-width: 0) {
            .acn-header-placeholder {
                height: 48px;
                width: 100%;
            }
        }

        @media (min-width: 980px) {
            .acn-header-placeholder {
                height: 89px;
                width: 100%;
            }
        }

        .acn-header-placeholder {
            background-color: #1A1A1A;
        }

        .acn-header-service {
            position: absolute;
            top: 0;
            width: 100%;
        }
  </style>
  <div class="acn-header-container">
   <div class="acn-header-placeholder">
   </div>
   <div class="public_headerpage">
   </div>
  </div>
  <!-- BEGIN: Documentation Content -->
  <div class="content">
   <div class="container">
    <div class="row">
     <div class="col-md-12">
      <div class="bread-crumb hidden-sm hidden-xs">
       <ul>
        <li>
         <span>
         </span>
        </li>
       </ul>
      </div>
     </div>
    </div>
    <div class="single-page">
     <div class="row">
      <div class="col-md-2">
       <div class="documentation-navigation left-navigation hidden-xs hidden-sm">
        <div class="loader">
         <i class="circle">
         </i>
         <i class="circle">
         </i>
         <i class="circle">
         </i>
         <i class="circle">
         </i>
         <i class="circle">
         </i>
        </div>
       </div>
      </div>
      <div class="col-md-10 pure-content">
       <div class="select left-navigation-select hidden-md hidden-lg">
        <select>
         <option selected="selected">
          加载中...
         </option>
        </select>
        <span class="icon icon-arrow-top">
        </span>
       </div>
       <tags ms.date="09/30/2015" ms.service="dns" wacn.date="11/27/2015">
       </tags>
       <!-- BEGIN: Product-Detail-TopBanner -->
       <div class="common-banner col-top-banner" data-config="{'backgroundColor':'#e3f4ff', 'backgroundImage':'/Images/marketing-resource/css/images/service/product_banner_dns.png','imageHeight':'auto'}">
        <div class="common-banner-image">
         <div class="common-banner-title">
          <img src="/Images/marketing-resource/css/images/service/<EMAIL>"/>
          <h2>
           DNS
          </h2>
          <h4>
           在 Azure 中托管你的域以获取卓越的性能和高可用性。
          </h4>
         </div>
        </div>
       </div>
       <!-- END: Product-Detail-TopBanner -->
       <div class="pricing-page-section">
        <p>
         通过 Azure DNS，可以在 Azure 中托管 DNS 域，这样就能使用与其他 Azure 服务相同的凭据、计费方式和支持合同来管理
                                DNS。区域可以为公用，也可以为专用，但专用 DNS 区域只对虚拟网络中的 VM 可见。名称服务器的全球网络使用 Anycast
                                路由可提供卓越的性能和可用性。
        </p>
       </div>
       <!-- BEGIN: TAB-CONTROL -->
       <div class="technical-azure-selector tab-control-selector">
        <h2>
         定价详细信息
        </h2>
        <h3>
         DNS 专用解析程序
         <!--<sup class="wa-previewTag">预览</sup>-->
        </h3>
        <div class="tab-control-container tab-active" id="tabContent1">
         <table cellpadding="0" cellspacing="0" width="100%">
          <thead>
           <tr>
            <th align="left">
             <strong>
             </strong>
            </th>
            <th align="left">
             <strong>
              价格
             </strong>
            </th>
           </tr>
          </thead>
          <tbody>
           <tr>
            <td class="left_align">
             Azure DNS 专用解析程序入站终结点
            </td>
            <td class="left_align">
             1 个终结点 - ￥1144.8/月按比例分摊为小时(如果之前已停用)
            </td>
           </tr>
           <tr>
            <td class="left_align">
             Azure DNS 专用解析程序出站终结点
            </td>
            <td class="left_align">
             1 个终结点 - ￥1144.8/月按比例分摊为小时(如果之前已停用)
            </td>
           </tr>
           <tr>
            <td class="left_align">
             Azure DNS 专用解析程序规则集
            </td>
            <td class="left_align">
             1 个规则集 - ￥15.9 /月按比例分摊为小时(如果之前已停用)
            </td>
           </tr>
          </tbody>
         </table>
        </div>
        <!-- <p>Azure DNS 基于在 Azure 中托管的 DNS 区域数和接收的 DNS 查询数进行计费。</p> -->
        <!-- BEGIN: TAB-CONTAINER-1 -->
        <div class="tab-control-container tab-active" id="tabContent1">
         <!-- BEGIN: Table1-Content-->
         <!-- <div class="tags-date">
                                    <div class="ms-date">*以下价格均为含税价格。</div><br>
                                    <div class="ms-date">*每月价格估算基于每个月 744 小时的使用量。</div>
                                </div> -->
        
        <br>
        <p>Azure DNS 基于在 Azure 中托管的 DNS 区域数和接收的 DNS 查询数进行计费。</p>
        <table cellpadding="0" cellspacing="0" width="100%">
          <thead>
           <tr>
            <th align="left">
             <strong>
              DNS
             </strong>
            </th>
            <th align="left">
             <strong>
              公共和专用区域
             </strong>
            </th>
           </tr>
          </thead>
          <tbody>
           <tr>
            <td class="left_align">
                头 25 个托管的 DNS 区域
            </td>
            <td class="left_align">
            ￥3.98 每月每区域
             <sup>
              1
             </sup>
            </td>
           </tr>
           <tr>
            <td class="left_align">
                额外托管的 DNS 区域（超过 25 个）
            </td>
            <td class="left_align">
                ￥0.8 每月每区域
             <sup>
              1
             </sup>
            </td>
           </tr>
           <tr>
            <td class="left_align">
                头亿条 DNS 查询/月
            </td>
            <td class="left_align">
                ￥4.07/百万
             <sup>
              2
             </sup>
            </td>
           </tr>
          </tbody>
         </table>
        <div class = "tags-date">
            <div class = "ms-date">
          <sup>
           1
          </sup>
          每个区域的费用按日计算；不足一月则按比例收费。
          <br>
          <sup>
           2
          </sup>
          出于计费的目的，将对 Azure 订阅内的查询进行合计。
        </div>
        </div>
    
         <!-- END: Table1-Content-->
        </div>
        <!-- END: TAB-CONTAINER-1 -->
       </div>
       <!-- END: TAB-CONTROL -->
       <div class="pricing-page-section">
        <div class="more-detail">
         <h2>
          常见问题
         </h2>
         <em>
          全部展开
         </em>
         <ul>
          <li>
           <i class="icon icon-plus">
           </i>
           <div>
            <a id="dns-que-q1">
             什么是“DNS 区域”？它是否与 DNS 域相同？
            </a>
            <section>
             <p>
              域是域名系统中的唯一名称，例如“contoso.com”。
             </p>
             <p>
              DNS 区域用于托管某个特定域的 DNS 记录。例如，域“contoso.com”可能包含许多 DNS
                                                    记录，例如“mail.contoso.com”（用于邮件服务器）和“www.contoso.com”（用于网站）。
             </p>
             <p>
              通过 Azure DNS，你可以托管 DNS 区域，从而管理 Azure 中域的 DNS 记录。
             </p>
            </section>
           </div>
          </li>
          <li>
           <i class="icon icon-plus">
           </i>
           <div>
            <a id="dns-que-q2">
             我是否需要购买 DNS 域名才能使用 Azure DNS？
            </a>
            <section>
             <p>
              不一定。
             </p>
             <p>
              你无需购买域便可托管 Azure DNS 中的 DNS。即使没有域名称，你也可以随时创建 DNS 区域。
             </p>
             <p>
              但是，如果想要将 DNS 域链接到全球 DNS 层级（此层级可从全球任意位置启用 DNS 查询以查找 DNS 区域和使用 DNS
                                                    记录回答），则需要购买域名。
             </p>
            </section>
           </div>
          </li>
          <li>
           <i class="icon icon-plus">
           </i>
           <div>
            <a id="dns-que-q3">
             我是否可以通过 Azure DNS 购买 DNS 域名？
            </a>
            <section>
             <p>
              不需要。Azure DNS 当前不支持购买域名。要购买域，必须使用第三方域名注册商，该注册商通常会收取少量的年费。
             </p>
            </section>
           </div>
          </li>
         </ul>
        </div>
       </div>
       <div class="pricing-page-section">
        <h2>
         支持和服务级别协议
        </h2>
        <p>
         如有任何疑问或需要帮助，请访问
         <a href="../../../support/plans/index.html" id="dns-contact-page">
          Azure
                                    支持
         </a>
         选择自助服务或者其他任何方式联系我们获得支持。
        </p>
        <p>
         我们保证 DNS 查询至少在 99.99% 的时间将从至少其中一个 Azure DNS 名称服务器群集收到有效响应。若要了解有关我们的服务器级别协议的详细信息，请访问
         <a href="../../../support/sla/dns/index.html" id="pricing_dns_sla">
          服务级别协议
         </a>
         页。
        </p>
       </div>
       <!--BEGIN: Support and service code chunk-->
       <!--

            <h2>支持和服务级别协议</h2>
            <p>Azure 支持功能：</p>
            <p>我们免费向用户提供以下支持服务：</p>
            <table cellpadding="0" cellspacing="0" class="table-col6">
                <tr>
                    <th align="left">&nbsp;</th>
                    <th align="left"><strong>是否支持</strong></th>
                </tr>
                <tr>
                    <td>计费和订阅管理</td>
                    <td><i class="icon icon-tick"></i></td>
                </tr>
                <tr>
                    <td>服务仪表板</td>
                    <td><i class="icon icon-tick"></i></td>
                </tr>
                <tr>
                    <td>Web事件提交</td>
                    <td><i class="icon icon-tick"></i></td>
                </tr>
                <tr>
                    <td>中断/修复不受限制</td>
                    <td><i class="icon icon-tick"></i></td>
                </tr>
                <tr>
                    <td>电话支持</td>
                    <td><i class="icon icon-tick"></i></td>
                </tr>
                <tr>
                    <td>ICP备案支持</td>
                    <td><i class="icon icon-tick"></i></td>
                </tr>
            </table>
            <p>您可以<a href="/support/support-ticket-form/?l=zh-cn" id="vm-sla-sup">在线申请支持</a>，也可以通过服务热线联系我们。</p>
            <h2>服务热线：</h2>
            <ul>
                <li>400-089-0365</li>
                <li>010-84563652</li>
            </ul>
            <p>社区帮助：<a href="https://social.msdn.microsoft.com/Forums/zh-cn/home?forum=windowsazurezhchs" id="pricing_vm_help">访问MSDN</a></p>
            <p>更多支持信息，请访问<a href="/support/plans/" id="stor-sla-info">Azure 支持计划</a></p>
              
      -->
       <!--END: Support and service code chunk-->
       <!--BEGIN: Support and service code chunk-->
       <!--END: Support and service code chunk-->
      </div>
     </div>
    </div>
   </div>
  </div>
  <!-- END: Documentation Content -->
  <!-- BEGIN: Footer -->
  <div class="public_footerpage">
  </div>
  <!--END: Common sidebar-->
  <link href="../../../Static/CSS/Localization/zh-cn.css" rel="stylesheet"/>
  <script type="text/javascript">
   function getAntiForgeryToken() {
            var token = '<input name="__RequestVerificationToken" type="hidden" value="RQsoY_00fiqRrPwZ8ULWa8TXzBJrywPNzwer1WVZOH6QKXS-H4ROkoeY_jNj7Qc6gv4TyFHclqEWjJ_IU_NW-xnmU8DRNiMMXz19uzawW4w1" />';
            token = $(token).val();
            return token;
        }
        function setLocaleCookie(localeVal) {
            var Days = 365 * 10; // Ten year expiration
            var exp = new Date();

            var hostName = window.location.hostname;
            var secondLevelDomain = hostName.substring(hostName.lastIndexOf(".", hostName.lastIndexOf(".") - 1));

            exp.setTime(exp.getTime() + Days * 24 * 60 * 60 * 1000);
            document.cookie = "acn_selected_locale=" + localeVal + ";domain=" + secondLevelDomain + "; path=/;expires=" + exp.toGMTString();
        }

        setLocaleCookie(window.currentLocale);
  </script>
  <script type="text/javascript">
   var MARKETING_STORAGE = '/blob/marketing-resource/Content';
        var TECHNICAL_STORAGE = '/blob/tech-content/';
        var EXTERNAL_RESOURCE_STORAGE = '/blob/external-resource/';
        var EnablePricingSync = 'false';
  </script>
  <!-- BEGIN: Minified RequireJs -->
  <script src="../../../Static/Scripts/global.config.js" type="text/javascript">
  </script>
  <script src="../../../Static/Scripts/require.js" type="text/javascript">
  </script>
  <script src="../../../Static/Scripts/4552a1505e111da8c71780b349b683700a6158fa.js" type="text/javascript">
  </script>
  <!-- END: Minified RequireJs -->
  <!-- begin JSLL -->
  <script src="https://az725175.vo.msecnd.net/scripts/jsll-4.js">
  </script>
  <script type="text/javascript">
   (function () {
            var config = {
                cookiesToCollect: ["_mkto_trk"],
                syncMuid: true,
                ix: {
                    a: true,
                    g: true
                },
                coreData: {
                    appId: 'AzureCN',
                    market: 'zh-cn',
                }
            };
            awa.init(config);
        })();
  </script>
  <!-- end JSLL -->
  <script src="../../../Static/Scripts/plugins/cookie/jquery.cookie.js" type="text/javascript">
  </script>
  <script src="../../../Static/Scripts/wacndatatracker.js" type="text/javascript">
  </script>
  <script src="/common/useCommon.js" type="text/javascript">
  </script>
 </body>
</html>
