<!DOCTYPE html>
<html lang="zh-CN">
 <head>
  <meta charset="utf-8"/>
  <meta content="IE=edge" http-equiv="X-UA-Compatible"/>
  <meta content="width=device-width, initial-scale=1.0, maximum-scale=2.0" name="viewport"/>
  <meta content="Azure, Azure 防火墙, 定价" name="keywords"/>
  <meta content="Azure 防火墙定价详情页面；该防火墙是一款云和本地网络安全和分析工具。" name="description"/>
  <title>
   Azure 防火墙 - Azure 云计算
  </title>
  <link href="../../../Static/Favicon/apple-touch-icon.png" rel="apple-touch-icon" sizes="180x180"/>
  <link href="../../../Static/Favicon/favicon.ico" rel="shortcut icon" type="image/x-icon"/>
  <link href="../../../Static/Favicon/favicon.ico" rel="icon" type="image/x-icon"/>
  <link href="../../../Static/Favicon/manifest.json" rel="manifest"/>
  <link color="#0078D4" href="../../../Static/Favicon/safari-pinned-tab.svg" rel="mask-icon"/>
  <meta content="/Static/Favicon/browserconfig.xml" name="msapplication-config"/>
  <meta content="#ffffff" name="theme-color"/>
  <link href="https://azure.microsoft.com/pricing/details/azure-firewall/" rel="canonical"/>
  <!-- BEGIN: Azure UI Style -->
  <link href="../../../Static/CSS/azureui.min.css" rel="stylesheet"/>
  <link href="../../../Static/CSS/common.min.css" rel="stylesheet"/>
  <!-- END: Azure UI Style -->
  <!-- BEGIN: Page Style -->
  <!-- END: Page Style -->
  <!-- BEGIN: Minified Page Style -->
  <link href="../../../Static/CSS/a05a0e4e99489d7b3cb548e24414e01adf779d77.css" rel="stylesheet"/>
  <!-- END: Minified Page Style -->
  <link href="../../../StaticService/css/service.min.css" rel="stylesheet"/>
 </head>
 <body class="zh-cn">
  <script>
   window.requireUrlArgs = "2019/6/24 6:51:27";
        window.currentLocale = "zh-CN";        
        window.headerTimestamp = "2019/1/23 8:25:24";
        window.footerTimestamp = "2019/1/8 8:07:06";
        window.locFileTimestamp = "2018/11/29 7:49:17";

        window.AcnHeaderFooter = {
            IsEnableQrCode: true,
            CurrentLang: window.currentLocale.toLowerCase(),
        };
  </script>
  <style>
   @media (min-width: 0) {
            .acn-header-placeholder {
                height: 48px; 
                width: 100%;
            }
        }

        @media (min-width: 980px) {
            .acn-header-placeholder {
                height: 89px; 
                width: 100%;
            }
        }

        .acn-header-placeholder {
            background-color: #1A1A1A;
        }

        .acn-header-service{
            position: absolute; 
            top: 0;
            width: 100%;
        }
        .ul {
            overflow: hidden;
            background: #f4f5f6;
            padding: 40px 20px;
        }
        .ul>li {
            list-style: none;
            display: block;
            width: 50%;
            float: left;
        }
  </style>
  <div class="acn-header-container">
   <div class="acn-header-placeholder">
   </div>
   <div class="public_headerpage">
   </div>
  </div>
  <div class="hide-info" style="display:none;">
   <div class="bg-box">
    <div class="cover-bg">
    </div>
   </div>
   <div class="msg-box">
    <div class="pricing-unavailable-message">
     所选区域不可用
    </div>
   </div>
  </div>
  <!-- BEGIN: Documentation Content -->
  <div class="content">
   <div class="container">
    <div class="row">
     <div class="col-md-12">
      <div class="bread-crumb hidden-sm hidden-xs">
       <ul>
        <li>
         <span>
         </span>
        </li>
       </ul>
      </div>
     </div>
    </div>
    <div class="single-page">
     <div class="row">
      <div class="col-md-2">
       <div class="documentation-navigation left-navigation hidden-xs hidden-sm">
        <div class="loader">
         <i class="circle">
         </i>
         <i class="circle">
         </i>
         <i class="circle">
         </i>
         <i class="circle">
         </i>
         <i class="circle">
         </i>
        </div>
       </div>
      </div>
      <div class="col-md-10 pure-content">
       <div class="select left-navigation-select hidden-md hidden-lg">
        <select>
         <option selected="selected">
          加载中...
         </option>
        </select>
        <span class="icon icon-arrow-top">
        </span>
       </div>
       <tags ms.date="09/30/2015" ms.service="azure-firewall" wacn.date="11/27/2015">
       </tags>
       <!-- BEGIN: Product-Detail-TopBanner -->
       <div class="common-banner col-top-banner" data-config="{'backgroundColor':'#e3f4ff', 'backgroundImage':'/Images/marketing-resource/css/azure-firewall_banner.png','imageHeight':'auto'}">
        <div class="common-banner-image">
         <div class="common-banner-title">
          <img src="/Images/marketing-resource/css/azure-firewall.svg"/>
          <h2>
           Azure 防火墙
           <span>
            Azure Firewall
           </span>
          </h2>
          <h4>
           云和本地网络安全，用于保护 Azure 虚拟网络资源
          </h4>
         </div>
        </div>
       </div>
       <!-- END: Product-Detail-TopBanner -->
       <ul class="ul">
        <li>
         <h2>
          本机防火墙功能，同时内置有高可用性、提供无限的云扩展性且无需维护
         </h2>
        </li>
        <li>
         <p>
          Azure Firewall is a managed cloud-based network security service that protects your Azure Virtual Network resources. Azure Firewall can be seamlessly deployed, requires zero maintenance, and is highly available with unrestricted cloud scalability. Setting up an Azure Firewall is easy; with billing comprised of a fixed and variable fee.
         </p>
        </li>
       </ul>
       <br/>
       <!-- BEGIN: TAB-CONTROL -->
       <div class="technical-azure-selector pricing-detail-tab tab-dropdown">
        <div class="tab-container-container">
         <div class="tab-container-box">
          <div class="tab-container">
           <div class="dropdown-container software-kind-container" style="display:none;">
            <label>
             OS/软件:
            </label>
            <div class="dropdown-box os-tab-nav hidden-sm hidden-xs">
             <span class="selected-item">
              Azure 防火墙
             </span>
             <i class="icon">
             </i>
             <ol class="tab-items">
              <li class="active">
               <a data-href="#tabContent1" href="javascript:void(0)" id="home_azure_firewall">
                Azure 防火墙
               </a>
              </li>
             </ol>
            </div>
            <select class="dropdown-select software-box hidden-lg hidden-md" id="software-box">
             <option data-href="#tabContent1" selected="selected" value="azure-firewall">
              Azure 防火墙
             </option>
            </select>
           </div>
           <div class="dropdown-container region-container">
            <label>
             地区:
            </label>
            <div class="dropdown-box os-tab-nav hidden-sm hidden-xs">
             <span class="selected-item">
              中国东部 2
             </span>
             <i class="icon">
             </i>
             <ol class="tab-items">
              <!-- id 对应 soft-category 的 region -->
              <li class="active">
                <a data-href="#north-china3" href="javascript:void(0)" id="north-china3">
                 中国北部 3
                </a>
               </li>               
              <li>
               <a data-href="#east-china2" href="javascript:void(0)" id="east-china2">
                中国东部 2
               </a>
              </li>
              <li>
               <a data-href="#north-china2" href="javascript:void(0)" id="north-china2">
                中国北部 2
               </a>
              </li>
              <li>
               <a data-href="#east-china" href="javascript:void(0)" id="east-china">
                中国东部
               </a>
              </li>
              <li>
               <a data-href="#north-china" href="javascript:void(0)" id="north-china">
                中国北部
               </a>
              </li>             
             </ol>
            </div>
            <select class="dropdown-select region-box hidden-lg hidden-md" id="region-box">
                <option data-href="#north-china3" selected="selected" value="north-china3">
                中国北部 3
                </option>                 
             <option data-href="#east-china2" value="east-china2">
              中国东部 2
             </option>
             <option data-href="#north-china2" value="north-china2">
              中国北部 2
             </option>
             <option data-href="#east-china" value="east-china">
              中国东部
             </option>
             <option data-href="#north-china" value="north-china">
              中国北部
             </option>            
            </select>
            <br/>
            <br/>
            <br/>
            <br/>
            <br/>
           </div>
           <div class="clearfix">
           </div>
          </div>
         </div>
        </div>
        <!-- BEGIN: TAB-CONTAINER-1 -->
        <div class="tab-content">
         <!-- BEGIN: TAB-CONTENT-1 -->
         <div class="tab-panel" id="tabContent1">
          <!-- BEGIN: Tab level 2 navigator 2 -->
          <!-- BEGIN: Tab level 2 content 3 -->
          <ul class="tab-nav" style="display:none">
           <li class="active">
            <a data-href="#tabContent2" data-toggle="tab" href="javascript:void(0)" id="gpv1">
             常规用途 v1
            </a>
           </li>
          </ul>
          <div class="tab-content">
           <!-- BEGIN: TAB-CONTAINER-1 -->
           <div class="tab-panel" id="tabContent1">
            <!-- BEGIN: Table1-Content-->
            <div class="scroll-table" style="display: block;">
             <h2>
              Azure 防火墙
             </h2>
             <table cellpadding="0" cellspacing="0" id="azure_firewall_standard" width="100%">
              <tr>
               <th align="left" style="width: 384px;">
                <strong>
                </strong>
               </th>
               <th align="left">
                <strong>
                 基本
                </strong>
               </th>
               <th align="left">
                <strong>
                 标准
                </strong>
               </th>
               <th align="left">
                <strong>
                 高级
                </strong>
               </th>
              </tr>
              <tr>
               <td>
                部署
               </td>
               <td>
                ￥4.02 每部署小时
               </td>
               <td>
                ￥12.72 每部署小时
               </td>
               <td>
                ￥17.808 每部署小时
               </td>
              </tr>
              <tr>
               <td>
                数据处理
               </td>
               <td>
                ￥0.66 每已处理 GB
               </td>
               <td>
                ￥0.1632 每已处理 GB
               </td>
               <td>
                ￥0.1632 每已处理 GB
               </td>
              </tr>
             </table>
             <!-- <table cellpadding="0" cellspacing="0" width="100%" id="azure_firewall_premium">
                                    <tr>
                                        <th align="left" style="width: 768px;"><strong>&nbsp;</strong></th>
                                        <th align="left"><strong>Standard</strong></th>
                                        <!-- <th align="left"><strong>Premium*</strong></th> -->
             <!-- </tr> -->
             <!-- <tr>
                                        <td>部署</td>
                                        <td>不适用</td> -->
             <!-- <td>￥17.808 每部署小时</td> -->
             <!-- </tr>
                                    <tr>
                                        <td>数据处理</td>
                                        <td>不适用</td> -->
             <!-- <td>￥0.1632 每已处理 GB</td> -->
             <!-- </tr> -->
            </div>
            <!-- <div class="tags-date">
                                    <div class="ms-date"><sup>*</sup>在公共预览版中，高级 SKU 的价格可享 50% 的折扣</div><br>
                                    
                                </div> -->
            <div class="scroll-table" style="display: block;">
             <h2>
              具有安全虚拟中心的 Azure 防火墙
             </h2>
             <table cellpadding="0" cellspacing="0" id="azure_firewall_standard2" width="100%">
              <tr>
               <th align="left" style="width: 384px;">
                <strong>
                </strong>
               </th>
               <th align="left">
                <strong>
                 基本
                </strong>
               </th>
               <th align="left">
                <strong>
                 标准
                </strong>
               </th>
               <th align="left">
                <strong>
                 高级
                </strong>
               </th>
              </tr>
              <tr>
               <td>
                安全虚拟中心部署
               </td>
               <td>
                ￥2.5122 每部署小时
               </td>
               <td>
                ￥12.72 每部署小时
               </td>
               <td>
                ￥17.808 每部署小时
               </td>
              </tr>
              <tr>
               <td>
                安全虚拟中心已处理的数据
               </td>
               <td>
                ￥0.4134 每已处理 GB
               </td>
               <td>
                ￥0.1632 每已处理 GB
               </td>
               <td>
                ￥0.1632 每已处理 GB
               </td>
              </tr>
             </table>
            </div>
            <!-- <table cellpadding="0" cellspacing="0" width="100%" id="azure_firewall_standard3">
                                    <tr>
                                        <th align="left" style="width: 384px;"><strong>&nbsp;</strong></th>
                                        <th align="left"><strong>Standard</strong></th>
                                        <th align="left"style="width: 430px;" ><strong>Premium</strong></th>
                                    </tr>
                                    <tr>
                                        <td>安全虚拟中心部署</td>
                                        <td>￥12.72 每部署小时</td>
                                        <td>N/A</td>
                                    </tr>
                                    <tr>
                                        <td>安全虚拟中心已处理的数据</td>
                                        <td>￥0.1632 每已处理 GB</td>
                                        <td>N/A</td>
                                    </tr>
                                </table> -->
            <!-- <div class="tags-date">
                                    <div class="ms-date"><sup>*</sup>在公共预览版中，高级 SKU 的价格可享 50% 的折扣</div><br>
                                </div> -->
           </div>
           <!-- END: TAB-CONTAINER-1 -->
          </div>
         </div>
        </div>
       </div>
       <!-- END: TAB-CONTROL -->
       <div class="pricing-page-section">
        <div class="more-detail">
         <h2>
          常见问题
         </h2>
         <em>
          全部展开
         </em>
         <ul>
          <li>
           <i class="icon icon-plus">
           </i>
           <div>
            <a id="ih-que-q1">
             是否不足 1 小时按 1 小时计费？
            </a>
            <section>
             <p>
              可以。不足 1 小时按 1 小时计费。
             </p>
            </section>
           </div>
          </li>
          <li>
           <i class="icon icon-plus">
           </i>
           <div>
            <a id="ih-que-q2">
             除了 Azure 防火墙费用外，还收取任何计算费用吗？
            </a>
            <section>
             <p>
              不。你可像往常一样为其他资源付费。不就 Azure 防火墙收取任何计算费用。
             </p>
            </section>
           </div>
          </li>
          <li>
           <i class="icon icon-plus">
           </i>
           <div>
            <a id="ih-que-q3">
             此服务如何计费？
            </a>
            <section>
             <p>
              防火墙开发（不限规模）将按小时收取费用（费率固定）。此外，每次开发将针对防火墙处理的数据收取数据处理费用。
             </p>
            </section>
           </div>
          </li>
         </ul>
        </div>
       </div>
       <div class="pricing-page-section">
        <h2>
         支持和服务级别协议
        </h2>
        <p>
         如有任何疑问或需要帮助，请访问
         <a href="https://support.azure.cn/zh-cn/support/contact/" id="iot-contact-page">
          Azure 支持
         </a>
         选择自助服务或者其他任何方式联系我们获得支持。
        </p>
        <p>
         我们保证 Azure 防火墙至少在 99.95% 的时间可用。若要了解有关我们的服务器级别协议的详细信息，请访问
         <a href="../../../support/sla/azure-firewall/index.html" id="pricing_azure-firewall_sla">
          服务级别协议
         </a>
         页。
        </p>
       </div>
       <!--BEGIN: Support and service code chunk-->
       <!--

            <h2>支持和服务级别协议</h2>
            <p>Azure 支持功能：</p>
            <p>我们免费向用户提供以下支持服务：</p>
            <table cellpadding="0" cellspacing="0" class="table-col6">
                <tr>
                    <th align="left">&nbsp;</th>
                    <th align="left"><strong>是否支持</strong></th>
                </tr>
                <tr>
                    <td>计费和订阅管理</td>
                    <td><i class="icon icon-tick"></i></td>
                </tr>
                <tr>
                    <td>服务仪表板</td>
                    <td><i class="icon icon-tick"></i></td>
                </tr>
                <tr>
                    <td>Web事件提交</td>
                    <td><i class="icon icon-tick"></i></td>
                </tr>
                <tr>
                    <td>中断/修复不受限制</td>
                    <td><i class="icon icon-tick"></i></td>
                </tr>
                <tr>
                    <td>电话支持</td>
                    <td><i class="icon icon-tick"></i></td>
                </tr>
                <tr>
                    <td>ICP备案支持</td>
                    <td><i class="icon icon-tick"></i></td>
                </tr>
            </table>
            <p>您可以<a href="/support/support-ticket-form/?l=zh-cn" id="vm-sla-sup">在线申请支持</a>，也可以通过服务热线联系我们。</p>
            <h2>服务热线：</h2>
            <ul>
                <li>400-089-0365</li>
                <li>010-84563652</li>
            </ul>
            <p>社区帮助：<a href="https://social.msdn.microsoft.com/Forums/zh-cn/home?forum=windowsazurezhchs" id="pricing_vm_help">访问MSDN</a></p>
            <p>更多支持信息，请访问<a href="/support/plans/" id="stor-sla-info">Azure 支持计划</a></p>
              
        -->
       <!--END: Support and service code chunk-->
       <!--BEGIN: Support and service code chunk-->
       <!--END: Support and service code chunk-->
      </div>
     </div>
    </div>
   </div>
  </div>
  <!-- END: Documentation Content -->
  <!-- BEGIN: Footer -->
  <div class="public_footerpage">
  </div>
  <!--END: Common sidebar-->
  <link href="../../../Static/CSS/Localization/zh-cn.css" rel="stylesheet"/>
  <script src="/Static/Scripts/lib/jquery-1.12.3.min.js" type="text/javascript">
  </script>
  <script type="text/javascript">
   function getAntiForgeryToken() {
        var token = '<input name="__RequestVerificationToken" type="hidden" value="JfdjquJ41TWy-S8qrMfYjkqjmW3BDZjEMydbVF0ZwGM_2yZ8pJm_dfQly1oRzVL98KHy5U2zNhvqw5xW5lVGEbh7iDo1-Qv3lZAx67wl6Is1" />';
        token = $(token).val();
        return token;
    }
    function setLocaleCookie(localeVal) {
        var Days = 365 * 10; // Ten year expiration
        var exp = new Date();

        var hostName = window.location.hostname;
        var secondLevelDomain = hostName.substring(hostName.lastIndexOf(".", hostName.lastIndexOf(".") - 1));

        exp.setTime(exp.getTime() + Days * 24 * 60 * 60 * 1000);
        document.cookie = "acn_selected_locale=" + localeVal + ";domain=" + secondLevelDomain + "; path=/;expires=" + exp.toGMTString();
    }

    setLocaleCookie(window.currentLocale);
  </script>
  <script type="text/javascript">
   var MARKETING_STORAGE = '/blob/marketing-resource/Content';
    var TECHNICAL_STORAGE = '/blob/tech-content/';
    var EXTERNAL_RESOURCE_STORAGE = '/blob/external-resource/';
    var EnablePricingSync = 'false';
  </script>
  <!-- BEGIN: Minified RequireJs -->
  <script src="../../../Static/Scripts/global.config.js" type="text/javascript">
  </script>
  <script src="../../../Static/Scripts/require.js" type="text/javascript">
  </script>
  <script src="/Static/Scripts/pricing-page-detail.js" type="text/javascript">
  </script>
  <!-- END: Minified RequireJs -->
  <!-- begin JSLL -->
  <script src="https://az725175.vo.msecnd.net/scripts/jsll-4.js">
  </script>
  <script type="text/javascript">
   (function () {
            var config = {
                cookiesToCollect: ["_mkto_trk"],
                syncMuid: true,
                ix: {
                    a: true,
                    g: true
                },
                coreData: {
                    appId: 'AzureCN',
                    market: 'zh-cn',
                }
            };
            awa.init(config);
        })();
  </script>
  <!-- end JSLL -->
  <script src="../../../Static/Scripts/plugins/cookie/jquery.cookie.js" type="text/javascript">
  </script>
  <script src="../../../Static/Scripts/wacndatatracker.js" type="text/javascript">
  </script>
  <script src="/common/useCommon.js" type="text/javascript">
  </script>
 </body>
</html>
