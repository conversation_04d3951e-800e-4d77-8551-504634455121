<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="utf-8" />
    <meta content="IE=edge" http-equiv="X-UA-Compatible" />
    <meta content="width=device-width, initial-scale=1" name="viewport" />
    <meta content="获取应用配置（快速、可缩放的应用配置参数存储）的详细定价。无前期成本。无终止费用。只为自己使用的东西付费。" name="description" />
    <title>
        应用程序配置定价
    </title>
    <link href="../../../../Static/Favicon/apple-touch-icon.png" rel="apple-touch-icon" sizes="180x180" />
    <link href="../../../../Static/Favicon/favicon.ico" rel="shortcut icon" type="image/x-icon" />
    <link href="../../../../Static/Favicon/favicon.ico" rel="icon" type="image/x-icon" />
    <link href="../../../../Static/Favicon/manifest.json" rel="manifest" />
    <link color="#0078D4" href="../../../../Static/Favicon/safari-pinned-tab.svg" rel="mask-icon" />
    <meta content="/Static/Favicon/browserconfig.xml" name="msapplication-config" />
    <meta content="#ffffff" name="theme-color" />
    <link href="https://azure.microsoft.com/pricing/details/storage/managed-disks/" rel="canonical" />
    <!-- BEGIN: Azure UI Style -->
    <link href="../../../../Static/CSS/azureui.min.css" rel="stylesheet" />
    <link href="../../../../Static/CSS/common.min.css" rel="stylesheet" />
    <!-- END: Azure UI Style -->
    <!-- BEGIN: Page Style -->
    <!-- END: Page Style -->
    <!-- BEGIN: Minified Page Style -->
    <link href="../../../../Static/CSS/a05a0e4e99489d7b3cb548e24414e01adf779d77.css" rel="stylesheet" />
    <!-- END: Minified Page Style -->
    <link href="../../../../StaticService/css/service.min.css" rel="stylesheet" />
</head>

<body class="zh-cn">
    <script>
        window.requireUrlArgs = "2019/6/24 6:51:27";
        window.currentLocale = "zh-CN";
        window.headerTimestamp = "2019/1/23 8:25:24";
        window.footerTimestamp = "2019/1/8 8:07:06";
        window.locFileTimestamp = "2018/11/29 7:49:17";

        window.AcnHeaderFooter = {
            IsEnableQrCode: true,
            CurrentLang: window.currentLocale.toLowerCase(),
        };
    </script>
    <style>
        @media (min-width: 0) {
            .acn-header-placeholder {
                height: 48px;
                width: 100%;
            }
        }

        @media (min-width: 980px) {
            .acn-header-placeholder {
                height: 89px;
                width: 100%;
            }
        }

        .acn-header-placeholder {
            background-color: #1A1A1A;
        }

        .acn-header-service {
            position: absolute;
            top: 0;
            width: 100%;
        }
    </style>
    <div class="acn-header-container">
        <div class="acn-header-placeholder">
        </div>
        <div class="public_headerpage">
        </div>
    </div>
    <!-- BEGIN: Documentation Content -->
    <div class="content">
        <div class="container">
            <div class="row">
                <div class="col-md-12">
                    <div class="bread-crumb hidden-sm hidden-xs">
                        <ul>
                            <li>
                                <span>
                                </span>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
            <div class="single-page">
                <div class="row">
                    <div class="col-md-2">
                        <div class="documentation-navigation left-navigation hidden-xs hidden-sm">
                            <div class="loader">
                                <i class="circle">
                                </i>
                                <i class="circle">
                                </i>
                                <i class="circle">
                                </i>
                                <i class="circle">
                                </i>
                                <i class="circle">
                                </i>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-10 pure-content">
                        <div class="select left-navigation-select hidden-md hidden-lg">
                            <select>
                                <option selected="selected">
                                    加载中...
                                </option>
                            </select>
                            <span class="icon icon-arrow-top">
                            </span>
                        </div>
                        <tags ms.date="08/26/2020" ms.service="app-configuration" wacn.date="11/27/2015">
                        </tags>
                        <style type="text/css">
                            .pricing-detail-tab .tab-nav {
                                padding-left: 0 !important;
                                margin-top: 5px;
                                margin-bottom: 0;
                                overflow: hidden;
                            }

                            .pricing-detail-tab .tab-nav li {
                                list-style: none;
                                float: left;
                            }

                            .pricing-detail-tab .tab-nav li.active a {
                                border-bottom: 4px solid #00a3d9;
                            }

                            .pricing-detail-tab .tab-nav li.active a:hover {
                                border-bottom: 4px solid #00a3d9;
                            }

                            .pricing-detail-tab .tab-content .tab-panel {
                                display: none;
                            }

                            .pricing-detail-tab .tab-content .tab-panel.show-md {
                                display: block;
                            }

                            .pricing-detail-tab .tab-content .tab-panel .tab-nav li a {
                                padding-left: 5px;
                                padding-right: 5px;
                                color: #00a3d9;
                                background-color: #FFF;
                            }

                            .pricing-detail-tab .tab-content .tab-panel .tab-nav li.active a {
                                color: #FFF;
                                background-color: #00a3d9;
                            }

                            .pricing-detail-tab .tab-content .tab-panel .tab-nav li a:hover {
                                color: #FFF;
                                background-color: #00a3d9;
                            }

                            .pure-content .technical-azure-selector p a,
                            .pure-content .technical-azure-selector table a {
                                background: 0 0;
                                padding: 0;
                                margin: 0 6px;
                                height: 21px;
                                line-height: 22px;
                                font-size: 14px;
                                color: #00a3d9;
                                float: none;
                                display: inline;
                            }

                            .svg {
                                width: 50px;
                                float: left;
                                margin-right: 10px;
                            }

                            .link-a {
                                display: inline !important;
                                padding: 0;
                                background-color: transparent !important;
                                float: none !important;
                                color: #006FD4 !important;
                            }
                        </style>
                        <div class="hide-info" style="display:none;">
                            <div class="bg-box">
                                <div class="cover-bg">
                                </div>
                            </div>
                            <div class="msg-box">
                                <div class="pricing-unavailable-message">
                                    所选区域不可用
                                </div>
                            </div>
                        </div>
                        <div class="common-banner col-top-banner"
                            data-config="{'backgroundColor':'#e3f4ff', 'backgroundImage':'','imageHeight':'auto'}">
                            <div class="common-banner-image">
                                <div class="common-banner-title">
                                    <div class="svg">
                                        <svg aria-hidden="true" data-slug-id="app-configuration" role="presentation"
                                            viewbox="0 0 18 18" xmlns="http://www.w3.org/2000/svg">
                                            <defs>
                                                <lineargradient gradientunits="userSpaceOnUse"
                                                    id="app-configuration:bbaa244d-7a73-4def-ae33-b68783fa5b51-f3379804"
                                                    x1="9" x2="9" y1="15.63" y2="-2.51">
                                                    <stop offset="0" stop-color="#0078d4">
                                                    </stop>
                                                    <stop offset="0.16" stop-color="#1380da">
                                                    </stop>
                                                    <stop offset="0.53" stop-color="#3c91e5">
                                                    </stop>
                                                    <stop offset="0.82" stop-color="#559cec">
                                                    </stop>
                                                    <stop offset="1" stop-color="#5ea0ef">
                                                    </stop>
                                                </lineargradient>
                                                <lineargradient gradientunits="userSpaceOnUse"
                                                    id="app-configuration:fa74d148-d6d0-4c75-94f7-94732b8c62b0-3465b0ec"
                                                    x1="12.26" x2="12.26" y1="7.17" y2="17.21">
                                                    <stop offset="0" stop-color="#ffd70f">
                                                    </stop>
                                                    <stop offset="0.27" stop-color="#ffd310">
                                                    </stop>
                                                    <stop offset="0.54" stop-color="#ffc613">
                                                    </stop>
                                                    <stop offset="0.83" stop-color="#feb217">
                                                    </stop>
                                                    <stop offset="1" stop-color="#fea11b">
                                                    </stop>
                                                </lineargradient>
                                            </defs>
                                            <path
                                                d="M17.41,8.4a3.77,3.77,0,0,0-3.28-3.63A4.76,4.76,0,0,0,9.22.21,4.92,4.92,0,0,0,4.53,3.4,4.48,4.48,0,0,0,.59,7.73a4.58,4.58,0,0,0,4.74,4.4,2.75,2.75,0,0,0,.41,0h7.67a.64.64,0,0,0,.2,0A3.82,3.82,0,0,0,17.41,8.4Z"
                                                fill="url(#app-configuration:bbaa244d-7a73-4def-ae33-b68783fa5b51-f3379804)">
                                            </path>
                                            <path
                                                d="M8.14,16v-.6l0,0-.61-.21-.16-.41.31-.62,0-.07-.19-.19-.23-.23-.08,0-.6.3-.41-.11-.26-.67H5.32l0,0-.2.61L4.67,14,4,13.62,3.56,14l0,.08.3.59-.17.41L3,15.38V16l.09,0,.63.21.17.41-.32.69.42.42.08,0,.6-.3.41.17.26.72h.59l0-.09.21-.63.4-.17.7.32.42-.42,0-.08-.3-.59.11-.42Zm-2.51.55a.84.84,0,1,1,.83-.84h0A.84.84,0,0,1,5.63,16.51Z"
                                                fill="#76bc2d">
                                            </path>
                                            <path
                                                d="M17.28,12.64V11.47l-.06-.05L16,11l-.31-.8L16.31,9l.06-.13L16,8.47,15.56,8l-.16.08-1.17.6-.8-.23-.51-1.3H11.76l-.06.06-.4,1.19-.82.31L9.13,8.07l-.82.82.08.16L9,10.21l-.33.8-1.41.51v1.17l.17.05,1.24.41L9,14l-.64,1.35.82.83.16-.07,1.17-.6.8.33.51,1.41H13L13,17l.41-1.24.79-.33,1.37.63.82-.82-.08-.16L15.74,14l.23-.82Zm-4.91,1.08A1.64,1.64,0,1,1,14,12.07h0A1.65,1.65,0,0,1,12.37,13.72Z"
                                                fill="url(#app-configuration:fa74d148-d6d0-4c75-94f7-94732b8c62b0-3465b0ec)">
                                            </path>
                                            <path
                                                d="M12.37,13.72A1.64,1.64,0,1,1,14,12.07h0A1.65,1.65,0,0,1,12.37,13.72Z"
                                                fill="#fff">
                                            </path>
                                        </svg>
                                    </div>
                                    <h2>
                                        应用程序配置定价
                                    </h2>
                                    <h4>
                                        用于应用程序配置的快速、可缩放的参数存储
                                    </h4>
                                </div>
                            </div>
                        </div>
                        <div class="pricing-page-section">
                            <p>
                                为你所有的 Azure 应用获得托管的通用存储。通过避免耗时的重新部署，实时有效且可靠地管理配置，而不影响客户。Azure 应用程序配置旨在提高速度、可伸缩性和安全性。
                            </p>
                        </div>
                        <h2>
                            定价详细信息
                        </h2>
                        <div class="technical-azure-selector pricing-detail-tab tab-dropdown">
                            <div class="tab-container-container">
                                <div class="tab-container-box">
                                    <div class="tab-container">
                                        <div class="dropdown-container software-kind-container" style="display:none;">
                                            <label>
                                                OS/软件:
                                            </label>
                                            <div class="dropdown-box os-tab-nav hidden-sm hidden-xs">
                                                <span class="selected-item">
                                                    App Configuration
                                                </span>
                                                <i class="icon">
                                                </i>
                                                <ol class="tab-items">
                                                    <li class="active">
                                                        <a data-href="#tabContent1" href="javascript:void(0)"
                                                            id="home_App-Configuration">
                                                            App Configuration
                                                        </a>
                                                    </li>
                                                </ol>
                                            </div>
                                            <select class="dropdown-select software-box hidden-lg hidden-md"
                                                id="software-box">
                                                <option data-href="#tabContent1" selected="selected"
                                                    value="App Configuration">
                                                    App Configuration
                                                </option>
                                            </select>
                                        </div>
                                        <div class="dropdown-container region-container">
                                            <label>
                                                地区:
                                            </label>
                                            <div class="dropdown-box os-tab-nav hidden-sm hidden-xs">
                                                <span class="selected-item">
                                                    中国北部 3
                                                </span>
                                                <i class="icon">
                                                </i>
                                                <ol class="tab-items">
                                                    <li class="active">
                                                        <a data-href="#north-china3" href="javascript:void(0)"
                                                            id="north-china3">
                                                            中国北部 3
                                                        </a>
                                                    </li>
                                                    <li>
                                                        <a data-href="#east-china2" href="javascript:void(0)"
                                                            id="east-china2">
                                                            中国东部 2
                                                        </a>
                                                    </li>
                                                    <li>
                                                        <a data-href="#north-china2" href="javascript:void(0)"
                                                            id="north-china2">
                                                            中国北部 2
                                                        </a>
                                                    </li>
                                                    <!-- <li>
                                                        <a data-href="#east-china" href="javascript:void(0)"
                                                            id="east-china">
                                                            中国东部
                                                        </a>
                                                    </li>
                                                    <li>
                                                        <a data-href="#north-china" href="javascript:void(0)"
                                                            id="north-china">
                                                            中国北部
                                                        </a>
                                                    </li> -->
                                                </ol>
                                            </div>
                                            <select class="dropdown-select region-box hidden-lg hidden-md"
                                                id="region-box">
                                                <option data-href="#north-china3" value="north-china3">
                                                    中国北部 3
                                                </option>
                                                <option data-href="#east-china2" selected="selected"
                                                    value="east-china2">
                                                    中国东部 2
                                                </option>
                                                <option data-href="#north-china2" value="north-china2">
                                                    中国北部 2
                                                </option>
                                                <!-- <option data-href="#east-china" value="east-china">
                                                    中国东部
                                                </option>
                                                <option data-href="#north-china" value="north-china">
                                                    中国北部
                                                </option> -->
                                            </select>
                                        </div>
                                        <div class="clearfix">
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <!-- BEGIN: TAB-CONTAINER-1 -->
                            <div class="tab-content">
                                <!-- BEGIN: TAB-CONTENT-1 -->
                                <div class="tab-panel" id="tabContent1">
                                    <!-- BEGIN: Tab level 2 navigator 2 -->
                                    <!-- BEGIN: Tab level 2 content 3 -->
                                    <ul class="tab-nav" style="display:none">
                                        <li class="active">
                                            <a data-href="#tabContent2" data-toggle="tab" href="javascript:void(0)"
                                                id="gpv1">
                                                常规用途 v1
                                            </a>
                                        </li>
                                    </ul>
                                    <div class="tab-content">
                                        <!-- BEGIN: Table1-Content-->
                                        <div class="tab-panel" id="tabContent2">
                                            <!-- BEGIN: Table1-Content-->
                                            <div class="scroll-table" style="display: block;">
                                                <h4>
                                                    功能和配额
                                                </h4>
                                                <table cellpadding="0" cellspacing="0"
                                                    id="active-directory-standard-app-configuration" width="100%">
                                                    <thead>
                                                        <tr>
                                                            <th align="left" style="width: 300px;">
                                                            </th>
                                                            <th align="left">
                                                                免费
                                                            </th>
                                                            <th align="left">
                                                                开发人员
                                                            </th>
                                                            <th align="left">
                                                                标准
                                                            </th>
                                                        </tr>
                                                    </thead>
                                                    <tbody>
                                                        <tr>
                                                            <td>
                                                                用途
                                                            </td>
                                                            <td>
                                                                For evaluation and trial use
                                                            </td>
                                                            <td>
                                                                For low-volume development and testing use cases
                                                            </td>
                                                            <td>
                                                                For medium-volume use cases
                                                            </td>
                                                        </tr>
                                                    <tbody>
                                                        <tr>
                                                            <td>
                                                                每个订阅的资源数
                                                                <br>
                                                                (资源由单一配置存储组成)
                                                            </td>
                                                            <td>
                                                                每个区域 3 个
                                                            </td>
                                                            <td>
                                                                无限制
                                                            </td>
                                                            <td>
                                                                无限制
                                                            </td>
                                                        </tr>
                                                        <tr>
                                                            <td>
                                                                每个资源的存储空间
                                                            </td>
                                                            <td>
                                                                10 MB
                                                            </td>
                                                            <td>
                                                                500 MB
                                                            </td>
                                                            <td>
                                                                1 GB
                                                            </td>
                                                        </tr>
                                                        <tr>
                                                            <td>
                                                                修订历史记录
                                                            </td>
                                                            <td>
                                                                7 天
                                                            </td>
                                                            <td>
                                                                7 天
                                                            </td>
                                                            <td>
                                                                30 天
                                                            </td>
                                                        </tr>
                                                        <tr>
                                                            <td>
                                                                吞吐量
                                                            </td>
                                                            <td>
                                                                无保证吞吐量
                                                            </td>
                                                            <td>
                                                                无保证吞吐量
                                                            </td>
                                                            <td>
                                                                对于读取请求，最多允许每秒 300 个请求(RPS)，对于写入请求，则最多允许 60 个 RPS。
                                                            </td>
                                                        </tr>
                                                        <tr>
                                                            <td>
                                                                每个资源的请求配额
                                                            </td>
                                                            <td>
                                                                1,000/天
                                                                <br>（配额用尽后，将对所有请求返回 HTTP 状态代码 429，直到当天结束为止）
                                                            </td>
                                                            <td>
                                                                6,000 每小时
                                                                <br>（配额用尽后，系统可能会针对请求返回 HTTP 状态代码 429，表示请求过多，直到对应的小时结束为止）
                                                            </td>
                                                            <td>
                                                                30,000 每小时
                                                                <br>(配额用尽后，请求可能会返回 HTTP 状态代码 429，指示请求过多 -
                                                                直到小时结束)。对于启用了异地复制的资源，每个副本每小时 30,000 个。
                                                            </td>
                                                        </tr>
                                                        <tr>
                                                            <td>
                                                                SLA
                                                            </td>
                                                            <td>
                                                                无
                                                            </td>
                                                            <td>
                                                                无
                                                            </td>
                                                            <td>
                                                                99.9%**
                                                            </td>
                                                        </tr>
                                                        <tr>
                                                            <td>
                                                                功能
                                                            </td>
                                                            <td>
                                                                使用 Microsoft 托管密钥进行加密
                                                                <br />
                                                                HMAC 或 AAD 身份验证
                                                                <br />
                                                                RBAC 支持
                                                                <br />
                                                                托管标识
                                                                <br />
                                                                服务标记
                                                            </td>
                                                            <td>
                                                                All Free tier functionality plus: Private Link support
                                                            </td>
                                                            <td>
                                                                All Developer tier functionality plus:
                                                                <br>
                                                                使用客户管理的密钥进行加密
                                                                <br>
                                                                软删除
                                                                <br>
                                                                异地复制
                                                            </td>
                                                        </tr>
                                                        <tr>
                                                            <td>
                                                                每个资源的快照存储*
                                                            </td>
                                                            <td>
                                                                10 MB
                                                            </td>
                                                            <td>
                                                                500 MB
                                                            </td>
                                                            <td>
                                                                1 GB
                                                            </td>
                                                        </tr>
                                                        <tr>
                                                            <td>
                                                                Maximum Private Endpoints per resource
                                                            </td>
                                                            <td>
                                                                无
                                                            </td>
                                                            <td>
                                                                1
                                                            </td>
                                                            <td>
                                                                10
                                                            </td>
                                                        </tr>
                                                    </tbody>
                                                </table>
                                                <div class="tags-date">
                                                    <div class="ms-date">
                                                        <sup>
                                                            *
                                                        </sup>
                                                        快照存储配额是额外的，不计入每个资源的存储。
                                                        <br>
                                                        <sup>
                                                            **
                                                        </sup>
                                                        此 SLA 仅在存储至少包含一个副本时才适用。如果未配置任何副本，则 SLA 将为 99.9%。
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="scroll-table" style="display: block;">
                                                <h4>
                                                    定价信息
                                                </h4>
                                                <table cellpadding="0" cellspacing="0"
                                                    id="pricing-information-standard-app-configuration" width="100%">
                                                    <thead>
                                                        <tr>
                                                            <th align="left" style="width: 300px;">
                                                            </th>
                                                            <th align="left" style="width: 250px;">
                                                                免费
                                                            </th>
                                                            <th align="left">
                                                                开发人员
                                                            </th>
                                                            <th align="left">
                                                                标准
                                                            </th>
                                                        </tr>
                                                    </thead>
                                                    <tbody>
                                                        <tr>
                                                            <td>
                                                                每个存储的成本
                                                                <sup>
                                                                    1
                                                                </sup>
                                                            </td>
                                                            <td>
                                                                免费
                                                            </td>
                                                            <td>
                                                                每天每个存储 ￥1.29，加上￥4.32/10,000 个请求
                                                                <sup>
                                                                    2
                                                                </sup>
                                                                的超额费用。 前 3000 个请求包含在每天的费用中。其他请求将按超额计费。
                                                            </td>
                                                            <td>
                                                                每天每个存储 ￥12.21，加上￥0.65/10,000 个请求 的超额费用。
                                                                每个副本前 200,000 个请求包含在每天的费用中。其他请求将按超额计费。
                                                            </td>
                                                        </tr>
                                                        <tr>
                                                            <td>
                                                                每个副本的成本
                                                            </td>
                                                            <td>
                                                                不适用
                                                            </td>
                                                            <td>
                                                                不适用
                                                            </td>
                                                            <td>
                                                                每天每个副本 ￥12.21加上 每个副本每 10,000 个请求 ￥0.65的超额费用。
                                                                每个副本前 200,000 个请求包含在每天的费用中。其他请求将按超额计费。
                                                            </td>
                                                        </tr>
                                                    </tbody>
                                                </table>
                                                <div class="tags-date">
                                                    <div class="ms-date">
                                                        <sup>
                                                            1
                                                        </sup>
                                                        每个存储的成本不包括副本的价格。有关副本定价，请检查“每个副本的成本”。
                                                        <br>
                                                        <sup>
                                                            2
                                                        </sup>
                                                        按 1,000 分摊
                                                        <br>
                                                        对应用程序配置有疑问? 查看我们的<a class="link-a" style="font-size: 12px;"
                                                            href="https://docs.azure.cn/zh-cn/azure-app-configuration/faq"
                                                            aria-label="aLabel">常见问题解答</a>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="pricing-page-section">
                            <h2>
                                支持和服务级别协议
                            </h2>
                            <p>
                                如有任何疑问或需要帮助，请访问
                                <a href="https://support.azure.cn/zh-cn/support/contact" style="margin: 0px;">
                                    Azure 支持
                                </a>
                                选择自助服务或者其他任何方式联系我们获得支持。若要了解有关我们的服务器级别协议的详细信息，请访问
                                <a href="../../../support/sla/app-configuration/" style="margin: 0px;">
                                    服务级别协议
                                </a>
                                页。
                            </p>
                        </div>
                        <!--BEGIN: Support and service code chunk-->
                        <!--END: Support and service code chunk-->
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- END: Documentation Content -->
    <!-- BEGIN: Footer -->
    <div class="public_footerpage">
    </div>
    <!--END: Common sidebar-->
    <link href="../../../../Static/CSS/Localization/zh-cn.css" rel="stylesheet" />
    <script type="text/javascript">
        function getAntiForgeryToken() {
            var token =
                '<input name="__RequestVerificationToken" type="hidden" value="pp_xxKDshbtueTQmfHeAW98thEu6tY1pJ2-jULR15hLqSMGIANieFFwvAOlk-XxiH_alcAFK99NAwcIECWmXpVR8DnvNjMuxo3SsmH4iPvA1" />';
            token = $(token).val();
            return token;
        }

        function setLocaleCookie(localeVal) {
            var Days = 365 * 10; // Ten year expiration
            var exp = new Date();

            var hostName = window.location.hostname;
            var secondLevelDomain = hostName.substring(hostName.lastIndexOf(".", hostName.lastIndexOf(".") - 1));

            exp.setTime(exp.getTime() + Days * 24 * 60 * 60 * 1000);
            document.cookie = "acn_selected_locale=" + localeVal + ";domain=" + secondLevelDomain + "; path=/;expires=" + exp
                .toGMTString();
        }

        setLocaleCookie(window.currentLocale);
    </script>
    <script type="text/javascript">
        var MARKETING_STORAGE = '/blob/marketing-resource/Content';
        var TECHNICAL_STORAGE = '/blob/tech-content/';
        var EXTERNAL_RESOURCE_STORAGE = '/blob/external-resource/';
        var EnablePricingSync = 'false';
    </script>
    <!-- BEGIN: Minified RequireJs -->
    <script src="../../../../Static/Scripts/global.config.js" type="text/javascript">
    </script>
    <script src="../../../../Static/Scripts/require.js" type="text/javascript">
    </script>
    <script src="/Static/Scripts/pricing-page-detail.js" type="text/javascript">
    </script>
    <script src="../../../../Static/Scripts/2c76879c65af503b3c0d969c85243b9f28fdb5c1.js" type="text/javascript">
    </script>
    <!-- <script src='../../../../Static/Scripts/4552a1505e111da8c71780b349b683700a6158fa.js' type='text/javascript'> -->
    <!-- END: Minified RequireJs -->
    <!-- begin JSLL -->
    <script src="https://az725175.vo.msecnd.net/scripts/jsll-4.js">
    </script>
    <script type="text/javascript">
        (function () {
            var config = {
                cookiesToCollect: ["_mkto_trk"],
                syncMuid: true,
                ix: {
                    a: true,
                    g: true
                },
                coreData: {
                    appId: 'AzureCN',
                    market: 'zh-cn',
                }
            };
            awa.init(config);
        })();
    </script>
    <!-- end JSLL -->
    <script src="../../../../Static/Scripts/plugins/cookie/jquery.cookie.js" type="text/javascript">
    </script>
    <script src="../../../../Static/Scripts/wacndatatracker.js" type="text/javascript">
    </script>
    <script src="/common/useCommon.js" type="text/javascript">
    </script>
</body>

</html>