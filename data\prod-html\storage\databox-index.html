<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="utf-8" />
    <meta content="IE=edge" http-equiv="X-UA-Compatible" />
    <meta content="width=device-width, initial-scale=1.0, maximum-scale=2.0" name="viewport" />
    <meta content="Azure, 微软云, Azure SQL Server Stretch Database, 价格详情, 定价, 计费" name="keywords" />
    <meta
        content="了解 Azure SQL Server Stretch Database 的价格详情。不同的性能级别对应不同的价格，用户可根据需求选择适用的级别。1元试用即获1500元人民币的服务使用额度，也可直接购买成为 Azure 预付费客户。"
        name="description" />
    <title>
        Azure Data Box定价 - Azure云计算
    </title>
    <link href="../../../Static/Favicon/apple-touch-icon.png" rel="apple-touch-icon" sizes="180x180" />
    <link href="../../../Static/Favicon/favicon.ico" rel="shortcut icon" type="image/x-icon" />
    <link href="../../../Static/Favicon/favicon.ico" rel="icon" type="image/x-icon" />
    <link href="../../../Static/Favicon/manifest.json" rel="manifest" />
    <link color="#0078D4" href="../../../Static/Favicon/safari-pinned-tab.svg" rel="mask-icon" />
    <meta content="/Static/Favicon/browserconfig.xml" name="msapplication-config" />
    <meta content="#ffffff" name="theme-color" />
    <link href="https://azure.microsoft.com/pricing/details/sql-server-stretch-database/" rel="canonical" />
    <!-- BEGIN: Azure UI Style -->
    <link href="../../../Static/CSS/azureui.min.css" rel="stylesheet" />
    <link href="../../../Static/CSS/common.min.css" rel="stylesheet" />
    <!-- END: Azure UI Style -->
    <!-- BEGIN: Page Style -->
    <!-- END: Page Style -->
    <!-- BEGIN: Minified Page Style -->
    <link href="../../../../Static/CSS/a05a0e4e99489d7b3cb548e24414e01adf779d77.css" rel="stylesheet" />
    <!-- END: Minified Page Style -->
    <link href="../../../StaticService/css/service.min.css" rel="stylesheet" />
</head>

<body class="zh-cn">
    <script>
        window.requireUrlArgs = "2019/6/24 6:51:27";
        window.currentLocale = "zh-CN";
        window.headerTimestamp = "2019/1/23 8:25:24";
        window.footerTimestamp = "2019/1/8 8:07:06";
        window.locFileTimestamp = "2018/11/29 7:49:17";

        window.AcnHeaderFooter = {
            IsEnableQrCode: true,
            CurrentLang: window.currentLocale.toLowerCase(),
        };
    </script>
    <style>
        @media (min-width: 0) {
            .acn-header-placeholder {
                height: 48px;
                width: 100%;
            }
        }

        @media (min-width: 980px) {
            .acn-header-placeholder {
                height: 89px;
                width: 100%;
            }
        }

        .acn-header-placeholder {
            background-color: #1A1A1A;
        }

        .acn-header-service {
            position: absolute;
            top: 0;
            width: 100%;
        }
    </style>
    <style type="text/css">
        .pricing-detail-tab .tab-nav {
            padding-left: 0 !important;
            margin-top: 5px;
            margin-bottom: 0;
            overflow: hidden;
        }

        .pricing-detail-tab .tab-nav li {
            list-style: none;
            float: left;
        }

        .pricing-detail-tab .tab-nav li.active a {
            border-bottom: 4px solid #00a3d9;
        }

        .pricing-detail-tab .tab-nav li.active a:hover {
            border-bottom: 4px solid #00a3d9;
        }

        .pricing-detail-tab .tab-content .tab-panel {
            display: none;
        }

        .pricing-detail-tab .tab-content .tab-panel.show-md {
            display: block;
        }

        .pricing-detail-tab .tab-content .tab-panel .tab-nav li a {
            padding-left: 5px;
            padding-right: 5px;
            color: #00a3d9;
            background-color: #FFF;
        }

        .pricing-detail-tab .tab-content .tab-panel .tab-nav li.active a {
            color: #FFF;
            background-color: #00a3d9;
        }

        .pricing-detail-tab .tab-content .tab-panel .tab-nav li a:hover {
            color: #FFF;
            background-color: #00a3d9;
        }

        .pure-content .technical-azure-selector .tags-date a,
        .pure-content .technical-azure-selector p a,
        .pure-content .technical-azure-selector table a {
            background: 0 0;
            padding: 0;
            margin: 0 6px;
            height: 21px;
            line-height: 22px;
            font-size: 14px;
            color: #00a3d9;
            float: none;
            display: inline;
        }
    </style>
    <div class="acn-header-container">
        <div class="acn-header-placeholder">
        </div>
        <div class="public_headerpage">
        </div>
    </div>
    <!-- BEGIN: Documentation Content -->
    <div class="content">
        <div class="container">
            <div class="row">
                <div class="col-md-12">
                    <div class="bread-crumb hidden-sm hidden-xs">
                        <ul>
                            <li>
                                <span>
                                </span>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
            <div class="single-page">
                <div class="row">
                    <div class="col-md-2">
                        <div class="documentation-navigation left-navigation hidden-xs hidden-sm">
                            <div class="loader">
                                <i class="circle">
                                </i>
                                <i class="circle">
                                </i>
                                <i class="circle">
                                </i>
                                <i class="circle">
                                </i>
                                <i class="circle">
                                </i>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-10 pure-content">
                        <div class="select left-navigation-select hidden-md hidden-lg">
                            <select>
                                <option selected="selected">
                                    加载中...
                                </option>
                            </select>
                            <span class="icon icon-arrow-top">
                            </span>
                        </div>
                        <tags ms.date="07/16/2020" ms.service="data-box-disk" wacn.date="07/16/2020">
                        </tags>
                        <!-- BEGIN: Product-Detail-TopBanner -->
                        <div class="common-banner col-top-banner"
                            data-config="{'backgroundColor':'#e3f4ff', 'backgroundImage':'/Images/marketing-resource/css/sql-server-stretch%20database.png','imageHeight':'auto'}">
                            <div class="common-banner-image">
                                <div class="common-banner-title">
                                    <img src="https://www.azure.cn/Images/marketing-resource/css/<EMAIL>" />
                                    <h2>
                                        Azure Data Box
                                    </h2>
                                    <h4>
                                        可在脱机状态下将数据更轻松地移入和移出 Azure 的设备。
                                    </h4>
                                </div>
                            </div>
                        </div>
                        <!-- END: Product-Detail-TopBanner -->
                        <div class="pricing-page-section">
                            <p>
                                Data Box Disk是脱机数据传输设备，它们在数据中心和 Azure 之间往返运输。使用 Microsoft Azure Data Box
                                磁盘解决方案可以通过快速、经济、可靠的方式将 TB 量级的本地数据发送到 Azure。
                            </p>
                        </div>
                        <!-- BEGIN: TAB-CONTROL -->
                        <div class="technical-azure-selector pricing-detail-tab tab-dropdown tab-fixed-recover">
                            <div class="dropdown-container software-kind-container" style="display:none;">
                                <label>
                                    OS/软件:
                                </label>
                                <div class="dropdown-box os-tab-nav hidden-sm hidden-xs">
                                    <span class="selected-item">
                                        Storage Blobs
                                    </span>
                                    <i class="icon">
                                    </i>
                                    <ol class="tab-items">
                                        <li class="active">
                                            <a data-href="#tabContent1" href="javascript:void(0)"
                                                id="home_storage-blobs">
                                                Storage
                                                Blobs
                                            </a>
                                        </li>
                                    </ol>
                                </div>
                                <select class="dropdown-select software-box hidden-lg hidden-md" id="software-box">
                                    <option data-href="#tabContent1" selected="selected" value="Storage Blobs">
                                        Storage Blobs
                                    </option>
                                </select>
                            </div>
                            <div class="tab-content">
                                <div class="tab-panel" id="tabContent1">
                                    <div class="category-container-container">
                                        <div class="category-container-box">
                                            <div class="category-container">
                                                <span class="category-title hidden-lg hidden-md">
                                                    类别：
                                                </span>
                                                <ul class="os-tab-nav category-tabs hidden-xs hidden-sm">
                                                    <li class="active">
                                                        <a data-href="#tabContent1-4" href="javascript:void(0)"
                                                            id="home_storage_gpvhy">
                                                            Data Box
                                                        </a>
                                                    </li>
                                                    <li>
                                                        <a data-href="#tabContent1-1" href="javascript:void(0)"
                                                            id="home_storage_gpv2">
                                                            Data Box Disk
                                                        </a>
                                                    </li>
                                                    <!-- <li>
                                                        <a data-href="#tabContent1-2" href="javascript:void(0)"
                                                            id="home_storage_blobs">
                                                            Data Box Heavy
                                                        </a>
                                                    </li>
                                                    <li>
                                                        <a data-href="#tabContent1-3" href="javascript:void(0)"
                                                            id="home_storage_gpv1">
                                                            Data Box Gateway
                                                        </a>
                                                    </li> -->
                                                </ul>
                                                <select class="dropdown-select category-tabs hidden-lg hidden-md">
                                                    <option data-href="#tabContent1-4" id="home_storage_gpv2"
                                                        value="General Purpose V2 Hierarchical Namspace">
                                                        Data Box
                                                    </option>
                                                    <option data-href="#tabContent1-1" id="home_storage_gpv2"
                                                        value="General Purpose v2">
                                                        Data Box Disk
                                                    </option>
                                                    <!-- <option data-href="#tabContent1-2" id="home_storage_blobs"
                                                        value="Blob storage">
                                                        Data Box Heavy
                                                    </option>
                                                    <option data-href="#tabContent1-3" id="home_storage_gpv1"
                                                        value="General Purpose v1">
                                                        Data Box Gateway
                                                    </option> -->
                                                </select>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="tab-content">
                                        <div class="tab-panel" id="tabContent1-1">
                                            <div>
                                                <!-- BEGIN: Table1-Content-->
                                                <h2>
                                                    定价详细信息
                                                </h2>
                                                <table cellpadding="0" cellspacing="0" width="100%">
                                                    <tr>
                                                        <th align="left">
                                                            <strong>
                                                                服务
                                                            </strong>
                                                        </th>
                                                        <th align="left">
                                                            <strong>
                                                                单位
                                                            </strong>
                                                        </th>
                                                        <th align="left">
                                                            <strong>
                                                                价格
                                                                <sup>
                                                                    *
                                                                </sup>
                                                            </strong>
                                                        </th>
                                                    </tr>
                                                    <tr>
                                                        <td>
                                                            订单处理费
                                                        </td>
                                                        <td>
                                                            1 个单位
                                                        </td>
                                                        <td>
                                                            <span>
                                                                ￥318
                                                            </span>
                                                        </td>
                                                    </tr>
                                                    <tr>
                                                        <td>
                                                            每日磁盘使用费 (8TB)
                                                            <sup>
                                                                1,2
                                                            </sup>
                                                        </td>
                                                        <td>
                                                            /磁盘/每天
                                                        </td>
                                                        <td>
                                                            <span>
                                                                ￥63.6
                                                            </span>
                                                        </td>
                                                    </tr>
                                                    <tr>
                                                        <td>
                                                            标准运费
                                                            <sup>
                                                                3
                                                            </sup>
                                                        </td>
                                                        <td>
                                                            1个包
                                                        </td>
                                                        <td>
                                                            <span>
                                                                ￥127.2
                                                            </span>
                                                        </td>
                                                    </tr>
                                                </table>
                                                <!-- END: Table1-Content-->
                                                <!-- BEGIN: Table2-Content-->
                                                <p>
                                                    <sup>
                                                        1
                                                    </sup>
                                                    每个订单最多 5 个磁盘。
                                                </p>
                                                <p>
                                                    <sup>
                                                        2
                                                    </sup>
                                                    前三天（包括交付日）是宽限期，收取一个单位的每日使用费。超过 3 天宽限期后，将收取每日使用费。
                                                </p>
                                                <p>
                                                    <sup>
                                                        3
                                                    </sup>
                                                    运费是指一次全程往返。
                                                </p>
                                                <p>
                                                    <sup>
                                                        *
                                                    </sup>
                                                    将按标准
                                                    <a href="https://www.azure.cn/pricing/details/storage/">
                                                        存储费率和事务费用
                                                    </a>
                                                    单独收费。
                                                </p>
                                            </div>
                                            <div class="pricing-page-section">
                                                <div class="more-detail">
                                                    <h2>
                                                        常见问题
                                                    </h2>
                                                    <em>
                                                        全部展开
                                                    </em>
                                                    <ul>
                                                        <li>
                                                            <i class="icon icon-plus">
                                                            </i>
                                                            <div>
                                                                <a id="home_sql-stretch_question_compatible">
                                                                    我的账单时如何计算的？
                                                                </a>
                                                                <section>
                                                                    <p>
                                                                        订单达到终端状态（即订单已完成且无错误）时，客户需要付费。
                                                                    </p>
                                                                </section>
                                                            </div>
                                                        </li>
                                                        <li>
                                                            <i class="icon icon-plus">
                                                            </i>
                                                            <div>
                                                                <a id="home_sql-stretch_question_unit">
                                                                    我可以在本地保留Azure Data Box Disk多长时间？
                                                                </a>
                                                                <section>
                                                                    <p>
                                                                        Data Box Disk 最长可保留在本地 45 天。
                                                                    </p>
                                                                </section>
                                                            </div>
                                                        </li>
                                                        <li>
                                                            <i class="icon icon-plus">
                                                            </i>
                                                            <div>
                                                                <a id="home_sql-stretch_question_amount">
                                                                    是否收取丢失或损坏设备的费用？
                                                                </a>
                                                                <section>
                                                                    <p>
                                                                        相同。Data Box Disk 丢失或损坏的费用为 ￥15,900 。
                                                                    </p>
                                                                </section>
                                                            </div>
                                                        </li>
                                                    </ul>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="tab-panel" id="tabContent1-2">
                                           
                                        </div>
                                        <div class="tab-panel" id="tabContent1-3">
                                            
                                        </div>
                                        <div class="tab-panel" id="tabContent1-4">
                                            <div>
                                                <table cellpadding="0" cellspacing="0" width="100%">
                                                    <tr>
                                                        <th align="left">
                                                            <strong>
                                                                服务
                                                            </strong>
                                                        </th>
                                                        <th align="left">
                                                            <strong>
                                                                单位
                                                            </strong>
                                                        </th>
                                                        <th align="left">
                                                            <strong>
                                                                价格
                                                                <sup>
                                                                    *
                                                                </sup>
                                                            </strong>
                                                        </th>
                                                    </tr>
                                                    <tr>
                                                        <td>
                                                            服务费
                                                        </td>
                                                        <td>
                                                            1 个单位（包括 10 天）
                                                        </td>
                                                        <td>
                                                            <span>
                                                                ￥1500
                                                            </span>
                                                        </td>
                                                    </tr>
                                                    <tr>
                                                        <td>
                                                            超时费用（按天算）
                                                        </td>
                                                        <td>
                                                            1 天
                                                        </td>
                                                        <td>
                                                            <span>
                                                                ￥90
                                                            </span>
                                                        </td>
                                                    </tr>
                                                    <!-- <tr>
                                                        <td>
                                                            标准运费
                                                            <sup>
                                                                1
                                                            </sup>
                                                        </td>
                                                        <td>
                                                            1个包
                                                        </td>
                                                        <td>
                                                            <span>
                                                                TBD
                                                            </span>
                                                        </td>
                                                    </tr> -->
                                                </table>
                                                <!-- <p>
                                                    <sup>
                                                        1
                                                    </sup>
                                                    运费是指一次全程往返。
                                                </p> -->
                                                <p>
                                                    <sup>
                                                        *
                                                    </sup>
                                                    将按标准
                                                    <a
                                                        href="https://www.azure.cn/pricing/details/storage/blobs/index.html">
                                                        存储费率和事务费用
                                                    </a>
                                                    单独收费。
                                                </p>
                                            </div>
                                            <div class="pricing-page-section">
                                                <div class="more-detail">
                                                    <h2>
                                                        常见问题
                                                    </h2>
                                                    <em>
                                                        全部展开
                                                    </em>
                                                    <ul>
                                                        <li>
                                                            <i class="icon icon-plus">
                                                            </i>
                                                            <div>
                                                                <a id="home_sql-stretch_question_compatible">
                                                                    我的账单时如何计算的？
                                                                </a>
                                                                <section>
                                                                    <p>
                                                                        订单达到终端状态（即订单已完成且无错误）时，客户需要付费。
                                                                    </p>
                                                                </section>
                                                            </div>
                                                        </li>
                                                        <li>
                                                            <i class="icon icon-plus">
                                                            </i>
                                                            <div>
                                                                <a id="home_sql-stretch_question_unit">
                                                                    我可以在本地保留Azure Data Box Disk多长时间？
                                                                </a>
                                                                <section>
                                                                    <p>
                                                                        Data Box Disk 最长可保留在本地 45 天。
                                                                    </p>
                                                                </section>
                                                            </div>
                                                        </li>
                                                        <li>
                                                            <i class="icon icon-plus">
                                                            </i>
                                                            <div>
                                                                <a id="home_sql-stretch_question_amount">
                                                                    是否收取丢失或损坏设备的费用？
                                                                </a>
                                                                <section>
                                                                    <p>
                                                                        可以。丢失或损坏设备的成本为￥240,000。
                                                                    </p>
                                                                </section>
                                                            </div>
                                                        </li>
                                                        <li>
                                                            <i class="icon icon-plus">
                                                            </i>
                                                            <div>
                                                                <a id="home_sql-stretch_question_how">
                                                                    导出订单的费用是多少？
                                                                </a>
                                                                <section>
                                                                    <p>
                                                                        通过 Data Box 从 Azure 导出与导入到 Azure 的费用相同。但是，这会产生额外的<a href="https://www.azure.cn/pricing/details/data-transfer/index.html " target="_blank">带宽费用</a>用于数据移出Azure。
                                                                    </p>
                                                                </section>
                                                            </div>
                                                        </li>
                                                    </ul>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                        </div>
                        <!-- END: TAB-CONTROL -->
                        <div class="pricing-page-section">
                            <h2>
                                支持和 SLA
                            </h2>
                            <p>
                                我们为公开发布的所有 Azure 服务（包括 Azure Data Box）提供技术支持。计费和订阅管理支持免费提供。
                            </p>
                        </div>

                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- END: Documentation Content -->
    <!-- BEGIN: Footer -->
    <div class="public_footerpage">
    </div>
    <!--END: Common sidebar-->
    <link href="../../Static/CSS/Localization/zh-cn.css" rel="stylesheet" />
    <script type="text/javascript">
        function getAntiForgeryToken() {
            var token = '<input name="__RequestVerificationToken" type="hidden" value="sOcBEa3zeaUBIxzULTQZ_IOoW997OD2ro58tCsBGxzbxUSVi47e5mIfGKfTNhuEqAQzBpUn5069CMsUzWljg-TClia2yPFwaLW0JTKglluo1" />';
            token = $(token).val();
            return token;
        }
        function setLocaleCookie(localeVal) {
            var Days = 365 * 10; // Ten year expiration
            var exp = new Date();

            var hostName = window.location.hostname;
            var secondLevelDomain = hostName.substring(hostName.lastIndexOf(".", hostName.lastIndexOf(".") - 1));

            exp.setTime(exp.getTime() + Days * 24 * 60 * 60 * 1000);
            document.cookie = "acn_selected_locale=" + localeVal + ";domain=" + secondLevelDomain + "; path=/;expires=" + exp.toGMTString();
        }

        setLocaleCookie(window.currentLocale);
    </script>
    <script type="text/javascript">
        var MARKETING_STORAGE = '/blob/marketing-resource/Content';
        var TECHNICAL_STORAGE = '/blob/tech-content/';
        var EXTERNAL_RESOURCE_STORAGE = '/blob/external-resource/';
        var EnablePricingSync = 'false';
    </script>
    <!-- BEGIN: Minified RequireJs -->
    <script src="../../../../Static/Scripts/global.config.js" type="text/javascript">
    </script>
    <script src="../../../../Static/Scripts/require.js" type="text/javascript">
    </script>
    <script src="../../../../Static/Scripts/4552a1505e111da8c71780b349b683700a6158fa.js" type="text/javascript">
    </script>
    <!-- END: Minified RequireJs -->
    <!-- begin JSLL -->
    <script src="https://az725175.vo.msecnd.net/scripts/jsll-4.js">
    </script>
    <script type="text/javascript">
        (function () {
            var config = {
                cookiesToCollect: ["_mkto_trk"],
                syncMuid: true,
                ix: {
                    a: true,
                    g: true
                },
                coreData: {
                    appId: 'AzureCN',
                    market: 'zh-cn',
                }
            };
            awa.init(config);
        })();
    </script>
    <!-- end JSLL -->
    <script src="../../../../Static/Scripts/plugins/cookie/jquery.cookie.js" type="text/javascript">
    </script>
    <script src="../../../../Static/Scripts/wacndatatracker.js" type="text/javascript">
    </script>
    <script src="/common/useCommon.js" type="text/javascript">
    </script>
</body>

</html>