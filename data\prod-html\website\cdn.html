<!DOCTYPE html>
<html lang="zh-CN">
 <head>
  <meta charset="utf-8"/>
  <meta content="IE=edge" http-equiv="X-UA-Compatible"/>
  <meta content="width=device-width, initial-scale=1.0, maximum-scale=2.0" name="viewport"/>
  <meta content="Azure CDN 价格, 数据传输和内容传送，CDN 加速服务、网站加速,云CDN API加速 直播加速 应用加速 HTTPS HTTP2" name="keywords"/>
  <meta content="了解 Azure CDN 价格详情。Azure CDN 主要通过 CDN 出站数据传输进行定价。1元试用即获1500元人民币的服务使用额度，也可直接购买成为 Azure 预付费客户，尽享最高 99.99% 的服务级别协议。" name="description"/>
  <title>
   Azure CDN 内容传送网络服务报价_价格预算 -  Azure 云计算
  </title>
  <link href="../../../Static/Favicon/apple-touch-icon.png" rel="apple-touch-icon" sizes="180x180"/>
  <link href="../../../Static/Favicon/favicon.ico" rel="shortcut icon" type="image/x-icon"/>
  <link href="../../../Static/Favicon/favicon.ico" rel="icon" type="image/x-icon"/>
  <link href="../../../Static/Favicon/manifest.json" rel="manifest"/>
  <link color="#0078D4" href="../../../Static/Favicon/safari-pinned-tab.svg" rel="mask-icon"/>
  <meta content="/Static/Favicon/browserconfig.xml" name="msapplication-config"/>
  <meta content="#ffffff" name="theme-color"/>
  <link href="https://azure.microsoft.com/pricing/details/cdn/" rel="canonical"/>
  <!-- BEGIN: Azure UI Style -->
  <link href="../../../Static/CSS/azureui.min.css" rel="stylesheet"/>
  <link href="../../../Static/CSS/common.min.css" rel="stylesheet"/>
  <!-- END: Azure UI Style -->
  <!-- BEGIN: Page Style -->
  <!-- END: Page Style -->
  <!-- BEGIN: Minified Page Style -->
  <link href="../../../Static/CSS/a05a0e4e99489d7b3cb548e24414e01adf779d77.css" rel="stylesheet"/>
  <!-- END: Minified Page Style -->
  <link href="../../../StaticService/css/service.min.css" rel="stylesheet"/>
 </head>
 <body class="zh-cn">
  <script>
   window.requireUrlArgs = "2019/6/24 6:51:27";
        window.currentLocale = "zh-CN";        
        window.headerTimestamp = "2019/1/23 8:25:24";
        window.footerTimestamp = "2019/1/8 8:07:06";
        window.locFileTimestamp = "2018/11/29 7:49:17";

        window.AcnHeaderFooter = {
            IsEnableQrCode: true,
            CurrentLang: window.currentLocale.toLowerCase(),
        };
  </script>
  <style>
   @media (min-width: 0) {
            .acn-header-placeholder {
                height: 48px; 
                width: 100%;
            }
        }

        @media (min-width: 980px) {
            .acn-header-placeholder {
                height: 89px; 
                width: 100%;
            }
        }

        .acn-header-placeholder {
            background-color: #1A1A1A;
        }

        .acn-header-service{
            position: absolute; 
            top: 0;
            width: 100%;
        }
  </style>
  <div class="acn-header-container">
   <div class="acn-header-placeholder">
   </div>
   <div class="public_headerpage">
   </div>
  </div>
  <!-- BEGIN: Documentation Content -->
  <div class="content">
   <div class="container">
    <div class="row">
     <div class="col-md-12">
      <div class="bread-crumb hidden-sm hidden-xs">
       <ul>
        <li>
         <span>
         </span>
        </li>
       </ul>
      </div>
     </div>
    </div>
    <div class="single-page">
     <div class="row">
      <div class="col-md-2">
       <div class="documentation-navigation left-navigation hidden-xs hidden-sm">
        <div class="loader">
         <i class="circle">
         </i>
         <i class="circle">
         </i>
         <i class="circle">
         </i>
         <i class="circle">
         </i>
         <i class="circle">
         </i>
        </div>
       </div>
      </div>
      <div class="col-md-10 pure-content">
       <div class="select left-navigation-select hidden-md hidden-lg">
        <select>
         <option selected="selected">
          加载中...
         </option>
        </select>
        <span class="icon icon-arrow-top">
        </span>
       </div>
       <tags ms.date="09/30/2015" ms.service="cdn" wacn.date="11/27/2015">
       </tags>
       <!-- BEGIN: Product-Detail-TopBanner -->
       <div class="common-banner col-top-banner" data-config="{'backgroundColor':'#e3f4ff', 'backgroundImage':'/Images/marketing-resource/media/images/production/cdn.png','imageHeight':'auto'}">
        <div class="common-banner-image">
         <div class="common-banner-title">
          <img src="/Images/marketing-resource/media/images/production/<EMAIL>"/>
          <h2>
           CDN 内容分发网络
          </h2>
          <h4>
           广泛覆盖可确保安全、可靠的内容交付
          </h4>
         </div>
        </div>
       </div>
       <!-- END: Product-Detail-TopBanner -->
       <div class="pricing-page-section">
        <p>
         Azure CDN 通过将内容缓存到最靠近客户的位置来改进应用程序的性能。
        </p>
       </div>
       <!-- BEGIN: TAB-CONTROL -->
       <div class="technical-azure-selector tab-control-selector">
        <h2>
         定价详细信息
        </h2>
        <!-- BEGIN: TAB-CONTAINER-1 -->
        <div class="tab-control-container tab-active" id="tabContent1">
         <!-- BEGIN: Table1-Content-->
         <h3>
          CDN 出站数据传输
         </h3>
         <!--<p>自 2016 年 12 月 1 月起，将取消阶梯式收费，CDN 数据传输价格将按照目前 0 - 10 TB/月的价格进行收取。</p>-->
         <div class="tags-date">
          <div class="ms-date">
           *以下价格均为含税价格。
          </div>
          <br/>
          <div class="ms-date">
           *每月价格估算基于每个月 744 小时的使用量。
          </div>
         </div>
         <table cellpadding="0" cellspacing="0" width="100%">
          <tr>
           <th align="left">
            <strong>
             服务
            </strong>
           </th>
           <th align="center" colspan="2">
            <strong>
             标准版
             <sup>
              2
             </sup>
            </strong>
           </th>
           <th align="center">
            <strong>
             标准版 Plus
             <sup>
              3
             </sup>
            </strong>
           </th>
          </tr>
          <tr>
           <td>
            区域
           </td>
           <td align="center">
            Zone1
           </td>
           <td align="center">
            Zone2
           </td>
           <td align="center">
            Zone1
           </td>
          </tr>
          <tr>
           <td>
            CDN 数据传输（0 - 10 TB
            <sup>
             1
            </sup>
            /月 )
           </td>
           <td align="center">
            ¥0.19/GB
           </td>
           <td align="center">
            ¥0.396/GB
           </td>
           <td align="center">
            ¥0.228/GB
           </td>
          </tr>
          <tr>
           <td>
            CDN 数据传输（10 - 50 TB/月）
           </td>
           <td align="center">
            ¥0.16/GB
           </td>
           <td align="center">
            ¥0.347/GB
           </td>
           <td align="center">
            ¥0.198/GB
           </td>
          </tr>
          <tr>
           <td>
            CDN 数据传输（50 - 100 TB/月）
           </td>
           <td align="center">
            ¥0.13/GB
           </td>
           <td align="center">
            ¥0.297/GB
           </td>
           <td align="center">
            ¥0.169/GB
           </td>
          </tr>
          <tr>
           <td>
            CDN 数据传输（大于 100 TB/月）
           </td>
           <td align="center">
            ¥0.1/GB
           </td>
           <td align="center">
            ¥0.277/GB
           </td>
           <td align="center">
            ¥0.139/GB
           </td>
          </tr>
          <!--
                        <tr>
                            <td>CDN 数据传输（500 - 1024 TB/月）</td>
                            <td>¥0.22/GB</td>
                            <td>¥0.44/GB</td>
                        </tr>
                        <tr>
                            <td>CDN 数据传输（1024 - 5120 TB/月）</td>
                            <td>¥0.19/GB</td>
                            <td>¥0.40/GB</td>
                        </tr>
                        <tr>
                            <td>CDN 数据传输（超过 5120 TB/月）</td>
                            <td><a id="CDN_contact_us" href="https://support.azure.cn/zh-cn/support/contact">联系我们</a></td>
                            <td><a id="CDN_price_us" href="https://support.azure.cn/zh-cn/support/contact">联系我们</a></td>
                        </tr>
						-->
         </table>
         <div class="tags-date">
          <div class="ms-date">
           <sup>
            1
           </sup>
           1 TB = 1000 GB
          </div>
          <br/>
          <div class="ms-date">
           <sup>
            2
           </sup>
           标准版 CDN 是指静态内容加速，包括网页加速，下载加速，点播加速。
          </div>
          <br/>
          <div class="ms-date">
           <sup>
            3
           </sup>
           标准版 Plus 是指直播加速。
          </div>
          <br/>
         </div>
         <!-- END: Table1-Content-->
        </div>
        <!-- END: TAB-CONTAINER-1 -->
       </div>
       <!-- END: TAB-CONTROL -->
       <div class="pricing-page-section">
        <div class="more-detail">
         <h2>
          常见问题
         </h2>
         <em>
          全部展开
         </em>
         <ul>
          <li>
           <i class="icon icon-plus">
           </i>
           <div>
            <a id="CDN_price_region">
             哪些区域与 Zone 1 和 Zone 2 相对应？
            </a>
            <section>
             <p>
              CDN 数据传输定价基于处理传输的节点的位置，而不是基于最终用户的位置。下面的地理区域对应于上面列出的 Azure CDN 的区域：
             </p>
             <p>
              •	Zone 1 — 中国大陆 (不包含香港，台湾和澳门)
             </p>
             <p>
              •	Zone 2 — 北美，欧洲，亚太地区，中东，非洲，南美，更多信息请参考
              <a href="https://docs.azure.cn/cdn/cdn-pops">
               海外节点分布
              </a>
             </p>
            </section>
           </div>
          </li>
          <li>
           <i class="icon icon-plus">
           </i>
           <div>
            <a id="CDN_price_question2">
             我是否可以指定用来为最终用户提供服务的 CDN 数据中心？
            </a>
            <section>
             <p>
              不可以。该服务将根据最终用户的网络配置选择 CDN 数据中心，开发人员不能决定所用 CDN 数据中心。服务将按用户 ISP 首选的位置或从逻辑上判断“更近”（不一定是物理接近）的节点为用户提供服务。
             </p>
            </section>
           </div>
          </li>
          <li>
           <i class="icon icon-plus">
           </i>
           <div>
            <a id="CDN_price_premium-version">
             标准版，标准版 Plus 的区别是什么？
            </a>
            <section>
             <br/>
             <ul>
              <li>
               标准版 CDN 是指静态内容加速，包括网页加速，下载加速，点播加速。
              </li>
              <li>
               标准版 Plus 是指直播加速。
              </li>
             </ul>
            </section>
           </div>
          </li>
          <li>
           <i class="icon icon-plus">
           </i>
           <div>
            <a id="CDN_Peak_Value_Bandwidth_Billing_Explanation">
             带宽峰值计费说明？
            </a>
            <section>
             <br/>
             <p>
              带宽峰值计费主要包含月 95 计费和平均峰计费，需联系世纪互联进行设置。具体如下：
             </p>
             <p>
              <strong>
               1.月 95 计费
              </strong>
             </p>
             <p>
              按自然月作为计费周期。当日峰值带宽 &gt; 0 记为有效天。在一个自然月内，对计费订阅号取每一个有效天的每 5 分钟粒度的带宽值，并进行降序排序，去掉前 5% 的统计点，剩下的最大的统计点作为Max95，计费为Max95*当月有效天/当月的总天数。
             </p>
             <p>
              以一月 30 天，有效天数为 20 天为例：每 5 分钟取一个带宽值，每小时取 12 个点，有效点数为 12 * 24 * 20 = 5760 个点；将所有的点按带宽值降序排列，去掉前 5% 的点 5760 x 5% = 288 个点，即第 289 个点为Max95，计费为 Max95 * 单价*20/30。
             </p>
             <p>
              <strong>
               2.平均峰计费
              </strong>
             </p>
             <p>
              按自然月为计费周期。当日峰值带宽 &gt; 0 记为有效天。在一个自然月内，对计费订阅号取每一个有效天的峰值，取平均值得到平均峰带宽。计费为平均峰带宽*20/30。
             </p>
             <p>
              以一月 30 天，有效天是 20天为例，取有效天内的峰值带宽作为当日的有效带宽，以此类推取得所有有效天的带宽。然后取值有效天峰值的平均值Average(Peak1, Peak2, ..., Peak 20)，计费为Average(Peak1, Peak2, ..., Peak 20)*单价*20/30。
             </p>
            </section>
           </div>
          </li>
          <li>
           <i class="icon icon-plus">
           </i>
           <div>
            <a id="CDN_price_What_is_the_relationship_between_Content_Delivery_Network_flow_and_back-to-source_flow">
             CDN流量和回源流量关系？
            </a>
            <section>
             <br/>
             <ul>
              <li>
               CDN 流量表示缓存命中
              </li>
              <li>
               回源流量表示 MISS 部分
              </li>
             </ul>
            </section>
           </div>
          </li>
          <li>
           <i class="icon icon-plus">
           </i>
           <div>
            <a id="CDN_price_What_is_the_difference_between_log_traffic_and_paid_traffic">
             日志流量和计费流量的区别？
            </a>
            <section>
             <br/>
             <p>
              CDN 加速域名实际产生的网络流量数据比日志流量数据高出 7%-15%。此差异是因为日志流量数据属于应用层统计的流量，互联网传输过程除了应用日志统计的流量之外，还会有 TCP/IP 包头和 TCP 重传等带来的额外网络消耗。
             </p>
             <ul>
              <li>
               TCP/IP 包头的消耗：基于 HTTP 协议的互联网数据包大小是 1500 个字节，其中包含 TCP 和 IP 协议的 40 个字节的包头长度。包头部分产生的流量，无法被应用层统计到，所以应用层日志记录到的流量就只有 1460 个字节。因此，包头部分的流量占到应用层日志统计流量的 2.74%（40/1460），也就是 3% 左右数据误差。
              </li>
              <li>
               TCP 重传：由于互联网网络负载的波动，会导致 3-10% 左右的数据包会被互联网丢弃。但是服务器会根据 TCP 协议的重传机制对丢包数据进行重传。重传的数据流量也不会被应用层统计到。
              </li>
             </ul>
            </section>
           </div>
          </li>
          <li>
           <i class="icon icon-plus">
           </i>
           <div>
            <a id="CDN_price_question4">
             哪些因素会影响 CDN 本地缓存中内容的可用性，如何降低对频繁发出原始请求的需求？
            </a>
            <section>
             <p>
              CDN 本地缓存中内容的可用性（通常称为“缓存功效”或“卸载”）会受到许多因素的影响，其中包括：
             </p>
             <ul>
              <li>
               有效期（“最大有效期”）标头值
              </li>
              <li>
               开发人员的内容库的总大小（可以缓存的数量）
              </li>
              <li>
               活动的工作集（当前缓存的数量）
              </li>
              <li>
               流量（提供的数量）
              </li>
              <li>
               缓存改动（将对象添加到缓存的频率，或老化的频率）
              </li>
             </ul>
             <p>
              例如，如果开发人员用对内容的改动频率更频繁，流量更大，则此开发人员的缓存效率将低于其他用户，因为换入和换出对象的频率会更高。这会导致更高的存储和数据传输费用，因为需要的原始请求更多。
             </p>
             <p>
              为了降低发出原始请求的需求，您可以设置更长的最大有效期标头，从而允许 CDN 将对象保留更长时间。
             </p>
            </section>
           </div>
          </li>
          <li>
           <i class="icon icon-plus">
           </i>
           <div>
            <a id="CDN_price_higher-data-transmission">
             在每月传输的数据超过 10TB 后，是否按更高等级的费率对所有数据传输计费？
            </a>
            <section>
             <p>
              不。属于每个等级的使用量将按照针对该等级的费率计费。例如，如果您在区域 1 产生了 50TB 的标准版 CDN 数据传输，前 10TB 将按照每 GB ¥ 0.19 的费率计费，剩下的 40TB 将按照每 GB ¥ 0.16 的费率计费。
             </p>
            </section>
           </div>
          </li>
          <li>
           <i class="icon icon-plus">
           </i>
           <div>
            <a id="CDN_price_question3">
             CDN 收费中是否包含对存储进行请求，以便检索数据并将数据从存储传输到 CDN 位置的费用？
            </a>
            <section>
             <p>
              不包含。当 CDN 收到针对非位于边缘位置的对象请求时，将向 Azure 存储发出请求以获取数据。从存储读取数据以及将数据从存储传输到 CDN 的操作将按照标准的
              <a href="../data-transfer/index.html" id="CDN_price_data">
               数据传输
              </a>
              费率收费。
             </p>
            </section>
           </div>
          </li>
          <!-- <li>
                            <i class="icon icon-plus"></i>
                            <div>
                                <a id="CDN_price_question7">CDN 收费是否包括标准数据传输的费用？</a>
                                <section>
                                    <p>CDN 回源请求会产生标准数据流量，即当 CDN 节点没有客户请求的内容时会直接向原站（Azure 存储）请求数据，从存储读取数据以及将数据传出到 CDN 节点的操作产生的流量就是普通流量，请求数据为入站流量，服务器（Azure 存储）响应客户端请求为出站流量。</p>
                                </section>

                            </div>
                        </li>
                        <li>
                            <i class="icon icon-plus"></i>
                            <div>
                                <a id="CDN_price_question8">什么时候会产生 CDN 的流量?</a>
                                <section>
                                    <p>当客户端向 CDN 节点发出请求（传入）或 CDN 节点响应客户端的请求时（传出），会产生 CDN 流量。CDN 入站流量和 CDN 节点间流量不计费。</p>
                                </section>

                            </div>
                        </li> -->
         </ul>
        </div>
       </div>
       <div class="pricing-page-section">
        <h2>
         支持和服务级别协议
        </h2>
        <p>
         如有任何疑问或需要帮助，请访问
         <a href="https://support.azure.cn/zh-cn/support/contact" id="cdn-contact-page">
          Azure 支持
         </a>
         选择自助服务或者其他任何方式联系我们获得支持。
        </p>
        <p>
         我们保证至少在 99.9% 的时间 CDN 将响应客户端请求并交付请求的内容，而不会出现错误。我们将检查并接受来自您选择用来监控内容的任何商业上合理的独立度量系统的数据。您必须从测量系统中的标准代理列表中选择一组普遍可用的代理，这些代理通常可用且代表中华人民共和国的主要大城市区域中的至少五个不同的地理位置。若要了解有关我们的服务器级别协议的详细信息，请访问
         <a href="../../../support/sla/cdn/index.html" id="pricing_cdn_sla">
          服务级别协议
         </a>
         页。
        </p>
       </div>
       <!--BEGIN: Support and service code chunk-->
       <!--

            <h2>支持和服务级别协议</h2>
            <p>Azure 支持功能：</p>
            <p>我们免费向用户提供以下支持服务：</p>
            <table cellpadding="0" cellspacing="0" class="table-col6">
                <tr>
                    <th align="left">&nbsp;</th>
                    <th align="left"><strong>是否支持</strong></th>
                </tr>
                <tr>
                    <td>计费和订阅管理</td>
                    <td><i class="icon icon-tick"></i></td>
                </tr>
                <tr>
                    <td>服务仪表板</td>
                    <td><i class="icon icon-tick"></i></td>
                </tr>
                <tr>
                    <td>Web事件提交</td>
                    <td><i class="icon icon-tick"></i></td>
                </tr>
                <tr>
                    <td>中断/修复不受限制</td>
                    <td><i class="icon icon-tick"></i></td>
                </tr>
                <tr>
                    <td>电话支持</td>
                    <td><i class="icon icon-tick"></i></td>
                </tr>
                <tr>
                    <td>ICP备案支持</td>
                    <td><i class="icon icon-tick"></i></td>
                </tr>
            </table>
            <p>您可以<a href="/support/support-ticket-form/?l=zh-cn" id="vm-sla-sup">在线申请支持</a>，也可以通过服务热线联系我们。</p>
            <h2>服务热线：</h2>
            <ul>
                <li>400-089-0365</li>
                <li>010-84563652</li>
            </ul>
            <p>社区帮助：<a href="https://social.msdn.microsoft.com/Forums/zh-cn/home?forum=windowsazurezhchs" id="pricing_vm_help">访问MSDN</a></p>
            <p>更多支持信息，请访问<a href="/support/plans/" id="stor-sla-info">Azure 支持计划</a></p>
              
      -->
       <!--END: Support and service code chunk-->
       <!--BEGIN: Support and service code chunk-->
       <!--END: Support and service code chunk-->
      </div>
     </div>
    </div>
   </div>
  </div>
  <!-- END: Documentation Content -->
  <!-- BEGIN: Footer -->
  <div class="public_footerpage">
  </div>
  <!--END: Common sidebar-->
  <link href="../../../Static/CSS/Localization/zh-cn.css" rel="stylesheet"/>
  <script type="text/javascript">
   function getAntiForgeryToken() {
        var token = '<input name="__RequestVerificationToken" type="hidden" value="3oPhXExIDIwZ3R9jIzFi2mU5h8bBXGP038Br2Rb-26HvlU4FhWEuWxhsnVwdZ_UabCez4xa9dKfpt2hPA8rJIIfAkL-ppZAbXgMJ0RND1y01" />';
        token = $(token).val();
        return token;
    }
    function setLocaleCookie(localeVal) {
        var Days = 365 * 10; // Ten year expiration
        var exp = new Date();

        var hostName = window.location.hostname;
        var secondLevelDomain = hostName.substring(hostName.lastIndexOf(".", hostName.lastIndexOf(".") - 1));

        exp.setTime(exp.getTime() + Days * 24 * 60 * 60 * 1000);
        document.cookie = "acn_selected_locale=" + localeVal + ";domain=" + secondLevelDomain + "; path=/;expires=" + exp.toGMTString();
    }

    setLocaleCookie(window.currentLocale);
  </script>
  <script type="text/javascript">
   var MARKETING_STORAGE = '/blob/marketing-resource/Content';
    var TECHNICAL_STORAGE = '/blob/tech-content/';
    var EXTERNAL_RESOURCE_STORAGE = '/blob/external-resource/';
    var EnablePricingSync = 'false';
  </script>
  <!-- BEGIN: Minified RequireJs -->
  <script src="../../../Static/Scripts/global.config.js" type="text/javascript">
  </script>
  <script src="../../../Static/Scripts/require.js" type="text/javascript">
  </script>
  <script src="../../../Static/Scripts/4552a1505e111da8c71780b349b683700a6158fa.js" type="text/javascript">
  </script>
  <!-- END: Minified RequireJs -->
  <!-- begin JSLL -->
  <script src="https://az725175.vo.msecnd.net/scripts/jsll-4.js">
  </script>
  <script type="text/javascript">
   (function () {
            var config = {
                cookiesToCollect: ["_mkto_trk"],
                syncMuid: true,
                ix: {
                    a: true,
                    g: true
                },
                coreData: {
                    appId: 'AzureCN',
                    market: 'zh-cn',
                }
            };
            awa.init(config);
        })();
  </script>
  <!-- end JSLL -->
  <script src="../../../Static/Scripts/plugins/cookie/jquery.cookie.js" type="text/javascript">
  </script>
  <script src="../../../Static/Scripts/wacndatatracker.js" type="text/javascript">
  </script>
  <script src="/common/useCommon.js" type="text/javascript">
  </script>
 </body>
</html>
