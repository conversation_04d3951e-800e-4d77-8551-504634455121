<!DOCTYPE html>
<html lang="zh-CN">
 <head>
  <meta charset="utf-8"/>
  <meta content="IE=edge" http-equiv="X-UA-Compatible"/>
  <meta content="width=device-width, initial-scale=1.0, maximum-scale=2.0" name="viewport"/>
  <meta content="Azure, 站点恢复, 价格" name="keywords"/>
  <meta content="了解 Azure 站点恢复（Site Recovery）价格详情。Azure 站点恢复（Site Recovery）通过协调在辅助位置自动复制和恢复私有云，帮助你保护重要的服务。1元试用即获1500元人民币的服务使用额度，也可直接购买成为 Azure 预付费客户，尽享最高 99.99% 的服务级别协议。" name="description"/>
  <title>
   Azure站点恢复服务定价 - Azure云计算
  </title>
  <link href="../../../Static/Favicon/apple-touch-icon.png" rel="apple-touch-icon" sizes="180x180"/>
  <link href="../../../Static/Favicon/favicon.ico" rel="shortcut icon" type="image/x-icon"/>
  <link href="../../../Static/Favicon/favicon.ico" rel="icon" type="image/x-icon"/>
  <link href="../../../Static/Favicon/manifest.json" rel="manifest"/>
  <link color="#0078D4" href="../../../Static/Favicon/safari-pinned-tab.svg" rel="mask-icon"/>
  <meta content="/Static/Favicon/browserconfig.xml" name="msapplication-config"/>
  <meta content="#ffffff" name="theme-color"/>
  <link href="https://azure.microsoft.com/pricing/details/site-recovery/" rel="canonical"/>
  <!-- BEGIN: Azure UI Style -->
  <link href="../../../Static/CSS/azureui.min.css" rel="stylesheet"/>
  <link href="../../../Static/CSS/common.min.css" rel="stylesheet"/>
  <!-- END: Azure UI Style -->
  <!-- BEGIN: Page Style -->
  <!-- END: Page Style -->
  <!-- BEGIN: Minified Page Style -->
  <link href="../../../Static/CSS/a05a0e4e99489d7b3cb548e24414e01adf779d77.css" rel="stylesheet"/>
  <!-- END: Minified Page Style -->
  <link href="../../../StaticService/css/service.min.css" rel="stylesheet"/>
 </head>
 <body class="zh-cn">
  <script>
   window.requireUrlArgs = "2019/6/24 6:51:27";
        window.currentLocale = "zh-CN";        
        window.headerTimestamp = "2019/1/23 8:25:24";
        window.footerTimestamp = "2019/1/8 8:07:06";
        window.locFileTimestamp = "2018/11/29 7:49:17";

        window.AcnHeaderFooter = {
            IsEnableQrCode: true,
            CurrentLang: window.currentLocale.toLowerCase(),
        };
  </script>
  <style>
   @media (min-width: 0) {
            .acn-header-placeholder {
                height: 48px; 
                width: 100%;
            }
        }

        @media (min-width: 980px) {
            .acn-header-placeholder {
                height: 89px; 
                width: 100%;
            }
        }

        .acn-header-placeholder {
            background-color: #1A1A1A;
        }

        .acn-header-service{
            position: absolute; 
            top: 0;
            width: 100%;
        }
  </style>
  <div class="acn-header-container">
   <div class="acn-header-placeholder">
   </div>
   <div class="public_headerpage">
   </div>
  </div>
  <!-- BEGIN: Documentation Content -->
  <div class="content">
   <div class="container">
    <div class="row">
     <div class="col-md-12">
      <div class="bread-crumb hidden-sm hidden-xs">
       <ul>
        <li>
         <span>
         </span>
        </li>
       </ul>
      </div>
     </div>
    </div>
    <div class="single-page">
     <div class="row">
      <div class="col-md-2">
       <div class="documentation-navigation left-navigation hidden-xs hidden-sm">
        <div class="loader">
         <i class="circle">
         </i>
         <i class="circle">
         </i>
         <i class="circle">
         </i>
         <i class="circle">
         </i>
         <i class="circle">
         </i>
        </div>
       </div>
      </div>
      <div class="col-md-10 pure-content">
       <div class="select left-navigation-select hidden-md hidden-lg">
        <select>
         <option selected="selected">
          加载中...
         </option>
        </select>
        <span class="icon icon-arrow-top">
        </span>
       </div>
       <tags ms.date="09/30/2015" ms.service="site-recovery" wacn.date="11/27/2015">
       </tags>
       <!-- BEGIN: Product-Detail-TopBanner -->
       <div class="common-banner col-top-banner" data-config="{'backgroundColor':'#e3f4ff', 'backgroundImage':'/Images/marketing-resource/media/images/production/site_recovery.png','imageHeight':'auto'}">
        <div class="common-banner-image">
         <div class="common-banner-title">
          <img src="/Images/marketing-resource/media/images/production/<EMAIL>"/>
          <h2>
           站点恢复
           <span>
            Site Recovery
           </span>
          </h2>
          <h4>
           安排私有云的保护和恢复
          </h4>
         </div>
        </div>
       </div>
       <!-- END: Product-Detail-TopBanner -->
       <div class="pricing-page-section">
        <h2>
         自动复制以帮助保护你的服务
        </h2>
        <p>
         Azure 站点恢复通过协调在辅助位置自动复制和恢复私有云，帮助你保护重要的服务。有两种主要的部署选项。
        </p>
        <ul>
         <li>
          在使用 Hyper-V 和系统中心的环境中运行的虚拟机可以在两个客户站点之间进行复制。Azure 站点恢复监视在主站点运行的应用程序的状况、存储恢复计划并且在需要时执行该计划。在主数据中心发生站点中断时，虚拟机通过安排的方式恢复，以便帮助快速恢复服务。还可以使用这一流程对恢复进行测试，或者暂时传输服务。
         </li>
         <li>
          现在可将虚拟机从主站点直接复制到 Azure，而不是复制到你自己的辅助站点。在主站点发生中断时，该服务会协调 Azure 中的虚拟机恢复。
         </li>
        </ul>
       </div>
       <!-- BEGIN: TAB-CONTROL -->
       <div class="technical-azure-selector tab-control-selector">
        <div class="tab-control-container tab-active">
         <div class="scroll-table" style="display: block;">
          <h2>
           定价详细信息
          </h2>
          <p>
           Azure 站点恢复按照受保护的实例的数量进行计费。
          </p>
          <!-- <p>从2016年4月1日起， 价格会下调 25.5%，以下是下调后的新价格：</p> -->
          <div class="tags-date">
           <div class="ms-date">
            *以下价格均为含税价格。
           </div>
           <br/>
           <div class="ms-date">
            *每月价格估算基于每个月 744 小时的使用量。
           </div>
          </div>
          <table cellpadding="0" cellspacing="0" width="100%">
           <tbody>
            <tr>
             <th align="left">
              <strong>
              </strong>
             </th>
             <th align="left">
              <strong>
               前 31 天的价格
              </strong>
             </th>
             <th align="left">
              <strong>
               31 天后的价格
              </strong>
             </th>
            </tr>
            <tr>
             <td>
              Azure 站点恢复到客户拥有站点
             </td>
             <td>
              免费
             </td>
             <td>
              每个受保护的实例 ¥101.2724/月
             </td>
            </tr>
            <tr>
             <td>
              Azure 站点恢复到 Azure
             </td>
             <td>
              免费
             </td>
             <td>
              每个受保护的实例 ¥254.4/月
             </td>
            </tr>
           </tbody>
          </table>
          <p>
           另外，存储、存储事务和数据传输分开收费。
          </p>
          <p>
           Azure 站点恢复以每月受保护的实例的日均数为单位进行计费。例如，如果你在前半个月持续保护了 20 个实例，而在后半个月未保护任何虚拟机实例，则这个月的日均受保护实例的数量将是 10。
          </p>
         </div>
        </div>
       </div>
       <!-- END: TAB-CONTROL -->
       <div class="pricing-page-section">
        <div class="more-detail">
         <h2>
          常见问题
         </h2>
         <em>
          全部展开
         </em>
         <h3>
          常规
         </h3>
         <ul>
          <li>
           <i class="icon icon-plus">
           </i>
           <div>
            <a id="site-recovery-que-1">
             站点恢复是如何计费的？
            </a>
            <section>
             <p>
              Azure 站点恢复按照受保护的实例的数量进行计费。另外，存储、存储事务和数据传输分开收费。
             </p>
            </section>
           </div>
          </li>
          <li>
           <i class="icon icon-plus">
           </i>
           <div>
            <a id="site-recovery-que-2">
             我已成为 Azure Site Recovery 用户一个多月。对于每个受保护的实例，是否仍享受前 31 天免费？
            </a>
            <section>
             <p>
              是，与使用 Azure Site Recovery 的时间长短无关。在前 31 天内，每个受保护的实例不会产生任何 Azure Site Recovery 费用。例如，你在过去 6 个月内保护了 10 个实例，然后你将第 11 个实例连接到 Azure Site Recovery，则在连接后的前 31 天内，第 11 个实例不会产生任何 Azure Site Recovery 费用。而前 10 个实例将继续产生 Azure Site Recovery 费用，因为它们受到保护的时间已超过 31 天。
             </p>
            </section>
           </div>
          </li>
          <li>
           <i class="icon icon-plus">
           </i>
           <div>
            <a id="site-recovery-que-q3">
             在前 31 天的期限内，会产生其他 Azure 费用吗？
            </a>
            <section>
             <p>
              是，尽管受保护实例的 Azure Site Recovery 在前 31 天内为免费，你可能产生 Azure 存储、存储交易和数据传输费用。恢复后的虚拟机也可能产生 Azure 计算费用。
             </p>
            </section>
           </div>
          </li>
          <li>
           <i class="icon icon-plus">
           </i>
           <div>
            <a id="site-recovery-que-q4">
             使用 Azure Site Recovery 时，会产生哪些费用？
            </a>
            <section>
             <p>
              在使用 Site Recovery 时，将产生 Site Recovery 许可证、Azure 存储、存储交易和出站数据传输费用。
             </p>
             <p>
              Site Recovery 许可证按每个受保护的实例计数，其中实例为虚拟机或物理服务器。
             </p>
             <ul>
              <li>
               如果将虚拟机磁盘复制到标准存储帐户中，则 Azure 存储针对存储使用量收费。例如，如果源磁盘大小为 1 TB，且已使用 400 GB 存储空间，Site Recovery 将在 Azure 中创建一个 1 TB 的 VHD，但存储空间按 400 GB 收费（外加用于复制日志的存储空间量）。
              </li>
              <li>
               如果将虚拟机磁盘复制到高级存储帐户，则 Azure 存储按预配的存储大小收费，取整为最接近的高级存储磁盘选项。例如，如果源磁盘大小为 50 GB，Site Recovery 将在 Azure 中创建一个 50 GB 的磁盘，而 Azure 会将此磁盘映射到最接近的高级存储磁盘 (P10)。成本将根据 P10，而不是 50 GB 的磁盘大小进行计算。了解更多。如果正在使用高级存储，则还需要用于复制日志记录的标准存储帐户，并且用于这些日志的标准存储空间量也会被计费。
              </li>
              <li>
               在测试故障转移或故障转移前不会创建任何磁盘。在复制状态中，根据存储定价计算器的“页 blob 和磁盘”类别将产生存储费用。这些费用基于存储类型（高级或者标准）以及冗余类型（包括 LRS、GRS、RA-GRS 等）。
              </li>
              <li>
               如果选择“对故障转移使用托管磁盘”选项，将在故障转移或测试故障转移后对托管磁盘收费。复制过程中，托管磁盘不收费。在复制过程中，将产生“非托管磁盘和页 blob”类别下的存储费用。这些费用基于高级/标准存储类型和 LRS、GRS、RA-GRS 等冗余类型。
              </li>
             </ul>
             <p>
              例如，对于复制到有 128 GB OS 磁盘和 500 GB 数据磁盘的高级存储的 VM：
             </p>
             <ul>
              <li>
               1.在复制过程中，将产生 P10 和 P20 高级存储磁盘大小“非托管磁盘和页 blob”类别下的存储费用。要复制的磁盘的大小（128 GB 和 500 GB）将四舍五入到 P10(128 GB) 和 P20(512 GB) 中最接近的非托管磁盘大小以进行计费。 还将使用在复制过程中记录增量更改的标准存储帐户。还将收取基于这些日志的标准存储用量的“非托管磁盘和页 blob”类别下的存储费用。
              </li>
              <li>
               2.在测试故障转移过程中或故障转移到托管磁盘之后：将收取 P10 和 P20 高级托管磁盘的托管磁盘费用。
              </li>
             </ul>
             <p>
              例如：对于复制到有 32 GB OS 磁盘和 250 GB 数据磁盘的标准存储的 VM：
             </p>
             <ul>
              <li>
               1.在复制过程中，将产生标准存储“非托管磁盘和页 blob”类别下的存储费用。
              </li>
              <li>
               2.在测试故障转移过程中或故障转移到托管磁盘之后：将收取 S4(32 GB) 和 S15(256 GB) 标准托管磁盘的托管磁盘费用。可见，磁盘的大小（32 GB 和 250 GB）已四舍五入到 S4(32 GB) 和 S15(256 GB) 中最接近的托管磁盘大小。
              </li>
             </ul>
             <ul>
              <li>
               如果未选择“对故障转移使用托管磁盘”选项，将在故障转移后，根据存储定价计算器对“未托管磁盘和页 blob”的类别收取存储费用。这些费用基于存储类型（高级或者标准）以及冗余类型（包括 LRS、GRS、RA-GRS 等）。
              </li>
              <li>
               在稳定状态复制过程中收取存储交易费用，而在故障转移或测试故障转移后，针对常规虚拟机操作收取存储交易费用。但这些费用都是微不足道的。
              </li>
             </ul>
             <p>
              测试故障转移过程中也会产生费用，期间虚拟机、存储、流出量和存储交易均会产生成本。
             </p>
            </section>
           </div>
          </li>
         </ul>
        </div>
       </div>
       <!-- <div class="pricing-page-section">
                <h2>上市地区</h2>
                <p>站点恢复服务在以下区域中提供：</p>
                <table cellpadding="0" cellspacing="0" class="table-col6">
                    <tr>
                        <th align="left"><strong>地域</strong></th>
                        <th align="left"><strong>区域</strong></th>
                    </tr>
                    <tr>
                        <td>中国大陆</td>
                        <td>中国东部数据中心 , 中国北部数据中心</td>
                    </tr>
                </table> -->
       <div class="pricing-page-section">
        <h2>
         支持和服务级别协议
        </h2>
        <p>
         如有任何疑问或需要帮助，请访问
         <a href="https://support.azure.cn/zh-cn/support/contact" id="recovery-contact-page">
          Azure 支持
         </a>
         选择自助服务或者其他任何方式联系我们获得支持。
        </p>
        <p>
         对于为本地到本地的故障转移配置的每个受保护的实例，我们都保证站点恢复服务至少有 99.9% 的可用性。
        </p>
        <p>
         对于本地部署到 Azure 的计划和非计划故障转移的每个受保护实例，我们保证未加密受保护实例的恢复时间为 4 小时，加密受保护实例为 6 小时，由受保护实例的大小而定。
        </p>
        <p>
         若要了解有关我们的服务器级别协议的详细信息，请访问
         <a href="../../../support/sla/site-recovery/index.html" id="pricing_recovery_sla">
          服务级别协议
         </a>
         页。
        </p>
       </div>
       <!--BEGIN: Support and service code chunk-->
       <!--

            <h2>支持和服务级别协议</h2>
            <p>Azure 支持功能：</p>
            <p>我们免费向用户提供以下支持服务：</p>
            <table cellpadding="0" cellspacing="0" class="table-col6">
                <tr>
                    <th align="left">&nbsp;</th>
                    <th align="left"><strong>是否支持</strong></th>
                </tr>
                <tr>
                    <td>计费和订阅管理</td>
                    <td><i class="icon icon-tick"></i></td>
                </tr>
                <tr>
                    <td>服务仪表板</td>
                    <td><i class="icon icon-tick"></i></td>
                </tr>
                <tr>
                    <td>Web事件提交</td>
                    <td><i class="icon icon-tick"></i></td>
                </tr>
                <tr>
                    <td>中断/修复不受限制</td>
                    <td><i class="icon icon-tick"></i></td>
                </tr>
                <tr>
                    <td>电话支持</td>
                    <td><i class="icon icon-tick"></i></td>
                </tr>
                <tr>
                    <td>ICP备案支持</td>
                    <td><i class="icon icon-tick"></i></td>
                </tr>
            </table>
            <p>您可以<a href="/support/support-ticket-form/?l=zh-cn" id="vm-sla-sup">在线申请支持</a>，也可以通过服务热线联系我们。</p>
            <h2>服务热线：</h2>
            <ul>
                <li>400-089-0365</li>
                <li>010-84563652</li>
            </ul>
            <p>社区帮助：<a href="https://social.msdn.microsoft.com/Forums/zh-cn/home?forum=windowsazurezhchs" id="pricing_vm_help">访问MSDN</a></p>
            <p>更多支持信息，请访问<a href="/support/plans/" id="stor-sla-info">Azure 支持计划</a></p>
              
      -->
       <!--END: Support and service code chunk-->
       <!--BEGIN: Support and service code chunk-->
       <!--END: Support and service code chunk-->
      </div>
     </div>
    </div>
   </div>
  </div>
  <!-- END: Documentation Content -->
  <!-- BEGIN: Footer -->
  <div class="public_footerpage">
  </div>
  <!--END: Common sidebar-->
  <link href="../../../Static/CSS/Localization/zh-cn.css" rel="stylesheet"/>
  <script type="text/javascript">
   function getAntiForgeryToken() {
        var token = '<input name="__RequestVerificationToken" type="hidden" value="whmWgGg2CWdYF1nW_geFIGgZdgqfeVCxHx-WNp83Kz0nRuZCaHJqZlnqoU25r7V1YZk1WQ3SxQdXY4HlNenRAi5c5fBVVnBHDqIALUwcxn81" />';
        token = $(token).val();
        return token;
    }
    function setLocaleCookie(localeVal) {
        var Days = 365 * 10; // Ten year expiration
        var exp = new Date();

        var hostName = window.location.hostname;
        var secondLevelDomain = hostName.substring(hostName.lastIndexOf(".", hostName.lastIndexOf(".") - 1));

        exp.setTime(exp.getTime() + Days * 24 * 60 * 60 * 1000);
        document.cookie = "acn_selected_locale=" + localeVal + ";domain=" + secondLevelDomain + "; path=/;expires=" + exp.toGMTString();
    }

    setLocaleCookie(window.currentLocale);
  </script>
  <script type="text/javascript">
   var MARKETING_STORAGE = '/blob/marketing-resource/Content';
    var TECHNICAL_STORAGE = '/blob/tech-content/';
    var EXTERNAL_RESOURCE_STORAGE = '/blob/external-resource/';
    var EnablePricingSync = 'false';
  </script>
  <!-- BEGIN: Minified RequireJs -->
  <script src="../../../Static/Scripts/global.config.js" type="text/javascript">
  </script>
  <script src="../../../Static/Scripts/require.js" type="text/javascript">
  </script>
  <script src=" /Static/Scripts/pricing-page-detail.js" type="text/javascript">
  </script>
  <script src="../../../../Static/Scripts/2c76879c65af503b3c0d969c85243b9f28fdb5c1.js" type="text/javascript">
  </script>
  <!-- <script src='../../../Static/Scripts/4552a1505e111da8c71780b349b683700a6158fa.js' type='text/javascript'></script> -->
  <!-- END: Minified RequireJs -->
  <!-- begin JSLL -->
  <script src="https://az725175.vo.msecnd.net/scripts/jsll-4.js">
  </script>
  <script type="text/javascript">
   (function () {
            var config = {
                cookiesToCollect: ["_mkto_trk"],
                syncMuid: true,
                ix: {
                    a: true,
                    g: true
                },
                coreData: {
                    appId: 'AzureCN',
                    market: 'zh-cn',
                }
            };
            awa.init(config);
        })();
  </script>
  <!-- end JSLL -->
  <script src="../../../Static/Scripts/plugins/cookie/jquery.cookie.js" type="text/javascript">
  </script>
  <script src="../../../Static/Scripts/wacndatatracker.js" type="text/javascript">
  </script>
  <script src="/common/useCommon.js" type="text/javascript">
  </script>
 </body>
</html>
