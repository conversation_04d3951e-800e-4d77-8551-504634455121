{"version": "2.1", "last_updated": "2025-08-26", "total_products": 52, "future_capacity": 120, "categories": {"database": {"count": 5, "config_path": "products/database/", "products": ["mysql", "postgresql", "cosmos-db", "data-explorer", "database-migration"]}, "analysis": {"count": 5, "config_path": "products/analysis/", "products": ["power-bi-embedded", "analysis-services", "hdinsight", "ssis", "stream-analytics"], "large_html_products": []}, "ai-ml": {"count": 5, "config_path": "products/ai-ml/", "products": ["anomaly-detector", "search", "databricks", "form-recognizer", "metrics-advisor"]}, "integration": {"count": 3, "config_path": "products/integration/", "products": ["api-management", "event-grid", "service-bus"]}, "iot": {"count": 1, "config_path": "products/iot/", "products": ["iot-edge"]}, "identity": {"count": 3, "config_path": "products/identity/", "products": ["active-directory-b2c", "active-directory-ds", "multi-factor-authentication"]}, "storage": {"count": 2, "config_path": "products/storage/", "products": ["storage-files", "data-lake-storage"]}, "compute": {"count": 2, "config_path": "products/compute/", "products": ["app-service", "cloud-services"], "large_html_products": []}, "container": {"count": 5, "config_path": "products/container/", "products": ["container-apps", "container-instances", "container-registry", "kubernetes-service", "service-fabric"]}, "management": {"count": 2, "config_path": "products/management/", "products": ["azure-firewall", "backup"]}, "migration": {"count": 2, "config_path": "products/migration/", "products": ["azure-migrate", "site-recovery"]}, "websites": {"count": 3, "config_path": "products/websites/", "products": ["cdn", "notification-hubs", "signalr-service"]}, "dev-tool": {"count": 1, "config_path": "products/dev-tool/", "products": ["app-configuration"]}, "networking": {"count": 13, "config_path": "products/networking/", "products": ["application-gateway", "azure-nat-gateway", "core-control-plane", "dns", "ip-address", "load-balancer", "network-watcher", "route-server", "traffic-manager", "virtual-network", "virtual-network-manager", "virtual-wan", "vpn-gateway"]}}, "load_strategy": {"lazy_load": true, "cache_configs": true, "parallel_load": false, "cache_ttl_minutes": 30}, "processing_defaults": {"memory_limit_mb": 512, "timeout_seconds": 120, "chunk_size_kb": 1024, "large_file_threshold_mb": 2}}